# 🔐 Secure Credential Setup Guide

## Overview
This guide helps you set up secure credential management for the Shoonya API with OTP (Two-Factor Authentication) popup support.

## 🚨 Security Features Implemented

✅ **Credentials stored separately** in `cred.yml` (excluded from version control)  
✅ **OTP popup** for real-time entry (no hard-coding required)  
✅ **Git protection** via `.gitignore` patterns  
✅ **Input validation** and error handling  
✅ **Masked output** for sensitive data  

---

## 📋 Initial Setup

### 1. Update Your Credentials
Edit the `cred.yml` file with your actual trading account details:

```yaml
user    : 'YOUR_ACTUAL_USER_ID'     # Replace with your user ID
pwd     : 'YOUR_ACTUAL_PASSWORD'    # Replace with your password  
factor2 : ''                        # Leave empty - OTP will be prompted
vc      : 'YOUR_VENDOR_CODE'        # Replace with your vendor code
apikey  : 'YOUR_API_KEY'            # Replace with your API key
imei    : 'YOUR_IMEI'               # Replace with your IMEI
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

---

## 🔒 Security Best Practices

### ✅ What's Already Protected:
- `cred.yml` is in `.gitignore` - **never gets committed**
- OTP is entered via secure popup (not stored anywhere)
- Sensitive data is masked in console output
- Multiple credential file patterns are ignored

### ⚠️ Important Security Notes:
1. **Never commit** your `cred.yml` with real credentials
2. **Never share** your API key or password
3. **Always verify** the OTP popup is legitimate (it shows a lock icon 🔐)
4. **Close the popup** if you're not ready to enter OTP

---

## 🎯 How It Works

### Authentication Flow:
1. **Static credentials** loaded from `cred.yml`
2. **OTP popup** appears when you run the notebook
3. **Enter your 6-digit OTP** in the popup
4. **Secure login** to trading API
5. **Masked display** of loaded credentials

### OTP Popup Features:
- 🔐 **Secure input** (characters are hidden)
- ⏰ **Real-time entry** (no pre-storage)
- ❌ **Cancel support** (ESC or X button)
- 🔝 **Always on top** (won't get lost)
- ✅ **Validation** (ensures OTP is entered)

---

## 🚀 Usage

### In Jupyter Notebook:
```python
# The secure authentication is now integrated
# Just run the credential cell and follow the popup
```

### Expected Output:
```
Loading credentials securely...
🔐 Please enter your OTP in the popup window...
✅ Credentials loaded successfully!
👤 User: FA293523
🏢 Vendor Code: FA293523_U
📱 IMEI: abc1234
🔑 Password and API Key: [HIDDEN FOR SECURITY]
🔐 OTP: [ENTERED SECURELY]
```

---

## 🔧 Troubleshooting

### Common Issues:

#### "Credentials file not found"
- Ensure `cred.yml` exists in the project root
- Check file permissions

#### "OTP popup doesn't appear"
- Restart your Python kernel
- Check if tkinter is installed: `python -c "import tkinter"`
- On Linux: `sudo apt-get install python3-tk`

#### "Authentication failed"
- Verify all credentials in `cred.yml` are correct
- Ensure OTP is current (6-digit code from your app)
- Check if your account is active

#### "Import error for secure_auth"
- Ensure `secure_auth.py` is in the same directory
- Restart the notebook kernel

---

## 🛡️ File Security Status

| File | Status | Description |
|------|--------|-------------|
| `cred.yml` | 🔒 **Protected** | Contains credentials, ignored by git |
| `secure_auth.py` | 📝 **Public** | Security module, safe to commit |
| `.gitignore` | 📝 **Public** | Protection rules, safe to commit |
| `requirements.txt` | 📝 **Public** | Dependencies, safe to commit |

---

## 🔍 Advanced Security Options

### Custom Credential File:
```python
from secure_auth import SecureAuth
auth = SecureAuth('my_custom_creds.yml')
creds = auth.get_complete_credentials()
```

### Programmatic OTP (for automation):
```python
# Not recommended for production
auth = SecureAuth()
auth.credentials = auth.load_credentials()
auth.credentials['factor2'] = 'your_otp_here'
```

---

## ⚠️ Emergency Recovery

If you accidentally commit credentials:
1. **Immediately change** all passwords and API keys
2. **Revoke** the compromised API key from your broker dashboard
3. **Force push** a cleaned history (contact admin if needed)
4. **Generate new** credentials

---

## 📞 Support

For issues with this security setup:
1. Check the troubleshooting section above
2. Verify your `cred.yml` format matches the template
3. Ensure all dependencies are installed
4. Restart your Python environment

**Security is paramount** - when in doubt, regenerate your credentials! 