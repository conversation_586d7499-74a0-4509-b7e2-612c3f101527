{"success": true, "ticker": "BATAINDIA", "analysis_period": "12:00-15:30", "date": "20-06-2025", "performance_metrics": {"execution_time_seconds": 4.75, "execution_time_minutes": 0.08, "api_calls_used": 1, "minutes_analyzed": 210, "time_per_minute": 0.0226, "naive_estimated_api_calls": 420, "naive_estimated_time_seconds": 3150, "performance_improvement_factor": 662.6, "api_reduction_factor": 420.0, "efficiency_gain_percent": 66160.5, "total_signals_generated": 41, "positions_opened": 10, "positions_closed": 10, "signal_frequency_percent": 19.52, "ver4_logic_preserved": true, "two_stage_analysis": true, "exact_signal_functions": true, "precise_position_management": true, "data_points_fetched": 301, "data_reuse_efficiency": 420.0, "memory_efficiency": "Optimized vectorized operations"}, "minute_signals": [{"time": "12:00", "datetime": "2025-06-20 12:00:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:18", "window_end": "12:00", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "10:48", "window_end": "12:00", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:01", "datetime": "2025-06-20 12:01:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:19", "window_end": "12:01", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "10:49", "window_end": "12:01", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:02", "datetime": "2025-06-20 12:02:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:20", "window_end": "12:02", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "10:50", "window_end": "12:02", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:03", "datetime": "2025-06-20 12:03:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:21", "window_end": "12:03", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "10:51", "window_end": "12:03", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:04", "datetime": "2025-06-20 12:04:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:22", "window_end": "12:04", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "10:52", "window_end": "12:04", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:05", "datetime": "2025-06-20 12:05:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:23", "window_end": "12:05", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "10:53", "window_end": "12:05", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:06", "datetime": "2025-06-20 12:06:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "11:24", "window_end": "12:06", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "10:54", "window_end": "12:06", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "12:07", "datetime": "2025-06-20 12:07:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "11:25", "window_end": "12:07", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "10:55", "window_end": "12:07", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "12:08", "datetime": "2025-06-20 12:08:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "11:26", "window_end": "12:08", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "10:56", "window_end": "12:08", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "12:09", "datetime": "2025-06-20 12:09:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:27", "window_end": "12:09", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "10:57", "window_end": "12:09", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:10", "datetime": "2025-06-20 12:10:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:28", "window_end": "12:10", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "10:58", "window_end": "12:10", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:11", "datetime": "2025-06-20 12:11:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:29", "window_end": "12:11", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "10:59", "window_end": "12:11", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:12", "datetime": "2025-06-20 12:12:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:30", "window_end": "12:12", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:00", "window_end": "12:12", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:13", "datetime": "2025-06-20 12:13:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:31", "window_end": "12:13", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:01", "window_end": "12:13", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:14", "datetime": "2025-06-20 12:14:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:32", "window_end": "12:14", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:02", "window_end": "12:14", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "12:15", "datetime": "2025-06-20 12:15:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:33", "window_end": "12:15", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:03", "window_end": "12:15", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:16", "datetime": "2025-06-20 12:16:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:34", "window_end": "12:16", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:04", "window_end": "12:16", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:17", "datetime": "2025-06-20 12:17:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:35", "window_end": "12:17", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:05", "window_end": "12:17", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:18", "datetime": "2025-06-20 12:18:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:36", "window_end": "12:18", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "11:06", "window_end": "12:18", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "12:19", "datetime": "2025-06-20 12:19:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:37", "window_end": "12:19", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "11:07", "window_end": "12:19", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "12:20", "datetime": "2025-06-20 12:20:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "11:38", "window_end": "12:20", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "11:08", "window_end": "12:20", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "12:21", "datetime": "2025-06-20 12:21:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:39", "window_end": "12:21", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "11:09", "window_end": "12:21", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "12:22", "datetime": "2025-06-20 12:22:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:40", "window_end": "12:22", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:10", "window_end": "12:22", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:23", "datetime": "2025-06-20 12:23:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:41", "window_end": "12:23", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:11", "window_end": "12:23", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:24", "datetime": "2025-06-20 12:24:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:42", "window_end": "12:24", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:12", "window_end": "12:24", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:25", "datetime": "2025-06-20 12:25:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:43", "window_end": "12:25", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:13", "window_end": "12:25", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:26", "datetime": "2025-06-20 12:26:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:44", "window_end": "12:26", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:14", "window_end": "12:26", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:27", "datetime": "2025-06-20 12:27:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:45", "window_end": "12:27", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:15", "window_end": "12:27", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:28", "datetime": "2025-06-20 12:28:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:46", "window_end": "12:28", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:16", "window_end": "12:28", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "12:29", "datetime": "2025-06-20 12:29:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:47", "window_end": "12:29", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:17", "window_end": "12:29", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "12:30", "datetime": "2025-06-20 12:30:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:48", "window_end": "12:30", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:18", "window_end": "12:30", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "12:31", "datetime": "2025-06-20 12:31:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:49", "window_end": "12:31", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:19", "window_end": "12:31", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "12:32", "datetime": "2025-06-20 12:32:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:50", "window_end": "12:32", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:20", "window_end": "12:32", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:33", "datetime": "2025-06-20 12:33:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:51", "window_end": "12:33", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:21", "window_end": "12:33", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:34", "datetime": "2025-06-20 12:34:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:52", "window_end": "12:34", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:22", "window_end": "12:34", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:35", "datetime": "2025-06-20 12:35:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:53", "window_end": "12:35", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:23", "window_end": "12:35", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:36", "datetime": "2025-06-20 12:36:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:54", "window_end": "12:36", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:24", "window_end": "12:36", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:37", "datetime": "2025-06-20 12:37:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:55", "window_end": "12:37", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:25", "window_end": "12:37", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:38", "datetime": "2025-06-20 12:38:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:56", "window_end": "12:38", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:26", "window_end": "12:38", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:39", "datetime": "2025-06-20 12:39:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:57", "window_end": "12:39", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:27", "window_end": "12:39", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:40", "datetime": "2025-06-20 12:40:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:58", "window_end": "12:40", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:28", "window_end": "12:40", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:41", "datetime": "2025-06-20 12:41:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:59", "window_end": "12:41", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:29", "window_end": "12:41", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:42", "datetime": "2025-06-20 12:42:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "12:00", "window_end": "12:42", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:30", "window_end": "12:42", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:43", "datetime": "2025-06-20 12:43:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:01", "window_end": "12:43", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:31", "window_end": "12:43", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "12:44", "datetime": "2025-06-20 12:44:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:02", "window_end": "12:44", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:32", "window_end": "12:44", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "12:45", "datetime": "2025-06-20 12:45:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:03", "window_end": "12:45", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:33", "window_end": "12:45", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:46", "datetime": "2025-06-20 12:46:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:04", "window_end": "12:46", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:34", "window_end": "12:46", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:47", "datetime": "2025-06-20 12:47:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "12:05", "window_end": "12:47", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "11:35", "window_end": "12:47", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:48", "datetime": "2025-06-20 12:48:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:06", "window_end": "12:48", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:36", "window_end": "12:48", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:49", "datetime": "2025-06-20 12:49:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:07", "window_end": "12:49", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:37", "window_end": "12:49", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:50", "datetime": "2025-06-20 12:50:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:08", "window_end": "12:50", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:38", "window_end": "12:50", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:51", "datetime": "2025-06-20 12:51:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:09", "window_end": "12:51", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:39", "window_end": "12:51", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:52", "datetime": "2025-06-20 12:52:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:10", "window_end": "12:52", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:40", "window_end": "12:52", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:53", "datetime": "2025-06-20 12:53:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:11", "window_end": "12:53", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:41", "window_end": "12:53", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:54", "datetime": "2025-06-20 12:54:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:12", "window_end": "12:54", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:42", "window_end": "12:54", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:55", "datetime": "2025-06-20 12:55:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:13", "window_end": "12:55", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:43", "window_end": "12:55", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:56", "datetime": "2025-06-20 12:56:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:14", "window_end": "12:56", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:44", "window_end": "12:56", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:57", "datetime": "2025-06-20 12:57:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:15", "window_end": "12:57", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:45", "window_end": "12:57", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:58", "datetime": "2025-06-20 12:58:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:16", "window_end": "12:58", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:46", "window_end": "12:58", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "12:59", "datetime": "2025-06-20 12:59:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:17", "window_end": "12:59", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:47", "window_end": "12:59", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:00", "datetime": "2025-06-20 13:00:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:18", "window_end": "13:00", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:48", "window_end": "13:00", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:01", "datetime": "2025-06-20 13:01:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:19", "window_end": "13:01", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:49", "window_end": "13:01", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:02", "datetime": "2025-06-20 13:02:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:20", "window_end": "13:02", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:50", "window_end": "13:02", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:03", "datetime": "2025-06-20 13:03:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:21", "window_end": "13:03", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:51", "window_end": "13:03", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:04", "datetime": "2025-06-20 13:04:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:22", "window_end": "13:04", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:52", "window_end": "13:04", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:05", "datetime": "2025-06-20 13:05:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:23", "window_end": "13:05", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:53", "window_end": "13:05", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:06", "datetime": "2025-06-20 13:06:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:24", "window_end": "13:06", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:54", "window_end": "13:06", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:07", "datetime": "2025-06-20 13:07:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:25", "window_end": "13:07", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:55", "window_end": "13:07", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:08", "datetime": "2025-06-20 13:08:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:26", "window_end": "13:08", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:56", "window_end": "13:08", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:09", "datetime": "2025-06-20 13:09:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:27", "window_end": "13:09", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:57", "window_end": "13:09", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:10", "datetime": "2025-06-20 13:10:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:28", "window_end": "13:10", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:58", "window_end": "13:10", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:11", "datetime": "2025-06-20 13:11:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:29", "window_end": "13:11", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "11:59", "window_end": "13:11", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:12", "datetime": "2025-06-20 13:12:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:30", "window_end": "13:12", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:00", "window_end": "13:12", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:13", "datetime": "2025-06-20 13:13:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:31", "window_end": "13:13", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:01", "window_end": "13:13", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:14", "datetime": "2025-06-20 13:14:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:32", "window_end": "13:14", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:02", "window_end": "13:14", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:15", "datetime": "2025-06-20 13:15:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:33", "window_end": "13:15", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:03", "window_end": "13:15", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:16", "datetime": "2025-06-20 13:16:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:34", "window_end": "13:16", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:04", "window_end": "13:16", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:17", "datetime": "2025-06-20 13:17:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "12:35", "window_end": "13:17", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "12:05", "window_end": "13:17", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "13:18", "datetime": "2025-06-20 13:18:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:36", "window_end": "13:18", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:06", "window_end": "13:18", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:19", "datetime": "2025-06-20 13:19:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:37", "window_end": "13:19", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:07", "window_end": "13:19", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:20", "datetime": "2025-06-20 13:20:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:38", "window_end": "13:20", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:08", "window_end": "13:20", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:21", "datetime": "2025-06-20 13:21:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:39", "window_end": "13:21", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:09", "window_end": "13:21", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:22", "datetime": "2025-06-20 13:22:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:40", "window_end": "13:22", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:10", "window_end": "13:22", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:23", "datetime": "2025-06-20 13:23:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:41", "window_end": "13:23", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:11", "window_end": "13:23", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:24", "datetime": "2025-06-20 13:24:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:42", "window_end": "13:24", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:12", "window_end": "13:24", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:25", "datetime": "2025-06-20 13:25:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:43", "window_end": "13:25", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:13", "window_end": "13:25", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:26", "datetime": "2025-06-20 13:26:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:44", "window_end": "13:26", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:14", "window_end": "13:26", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:27", "datetime": "2025-06-20 13:27:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:45", "window_end": "13:27", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:15", "window_end": "13:27", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:28", "datetime": "2025-06-20 13:28:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:46", "window_end": "13:28", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:16", "window_end": "13:28", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:29", "datetime": "2025-06-20 13:29:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:47", "window_end": "13:29", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:17", "window_end": "13:29", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:30", "datetime": "2025-06-20 13:30:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:48", "window_end": "13:30", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:18", "window_end": "13:30", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:31", "datetime": "2025-06-20 13:31:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:49", "window_end": "13:31", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:19", "window_end": "13:31", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:32", "datetime": "2025-06-20 13:32:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:50", "window_end": "13:32", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:20", "window_end": "13:32", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:33", "datetime": "2025-06-20 13:33:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:51", "window_end": "13:33", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:21", "window_end": "13:33", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:34", "datetime": "2025-06-20 13:34:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:52", "window_end": "13:34", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:22", "window_end": "13:34", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:35", "datetime": "2025-06-20 13:35:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:53", "window_end": "13:35", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:23", "window_end": "13:35", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:36", "datetime": "2025-06-20 13:36:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:54", "window_end": "13:36", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:24", "window_end": "13:36", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:37", "datetime": "2025-06-20 13:37:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:55", "window_end": "13:37", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:25", "window_end": "13:37", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:38", "datetime": "2025-06-20 13:38:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:56", "window_end": "13:38", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:26", "window_end": "13:38", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:39", "datetime": "2025-06-20 13:39:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:57", "window_end": "13:39", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:27", "window_end": "13:39", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:40", "datetime": "2025-06-20 13:40:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:58", "window_end": "13:40", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:28", "window_end": "13:40", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:41", "datetime": "2025-06-20 13:41:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:59", "window_end": "13:41", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:29", "window_end": "13:41", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:42", "datetime": "2025-06-20 13:42:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "13:00", "window_end": "13:42", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:30", "window_end": "13:42", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: PASS, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:43", "datetime": "2025-06-20 13:43:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:01", "window_end": "13:43", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:31", "window_end": "13:43", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:44", "datetime": "2025-06-20 13:44:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:02", "window_end": "13:44", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:32", "window_end": "13:44", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:45", "datetime": "2025-06-20 13:45:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:03", "window_end": "13:45", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:33", "window_end": "13:45", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:46", "datetime": "2025-06-20 13:46:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:04", "window_end": "13:46", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:34", "window_end": "13:46", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:47", "datetime": "2025-06-20 13:47:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:05", "window_end": "13:47", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:35", "window_end": "13:47", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:48", "datetime": "2025-06-20 13:48:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:06", "window_end": "13:48", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:36", "window_end": "13:48", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:49", "datetime": "2025-06-20 13:49:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:07", "window_end": "13:49", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:37", "window_end": "13:49", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:50", "datetime": "2025-06-20 13:50:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:08", "window_end": "13:50", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:38", "window_end": "13:50", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:51", "datetime": "2025-06-20 13:51:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:09", "window_end": "13:51", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:39", "window_end": "13:51", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:52", "datetime": "2025-06-20 13:52:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:10", "window_end": "13:52", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:40", "window_end": "13:52", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:53", "datetime": "2025-06-20 13:53:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "13:11", "window_end": "13:53", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "12:41", "window_end": "13:53", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "13:54", "datetime": "2025-06-20 13:54:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:12", "window_end": "13:54", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:42", "window_end": "13:54", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:55", "datetime": "2025-06-20 13:55:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:13", "window_end": "13:55", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:43", "window_end": "13:55", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:56", "datetime": "2025-06-20 13:56:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:14", "window_end": "13:56", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:44", "window_end": "13:56", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:57", "datetime": "2025-06-20 13:57:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:15", "window_end": "13:57", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:45", "window_end": "13:57", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:58", "datetime": "2025-06-20 13:58:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:16", "window_end": "13:58", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:46", "window_end": "13:58", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "13:59", "datetime": "2025-06-20 13:59:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:17", "window_end": "13:59", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:47", "window_end": "13:59", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:00", "datetime": "2025-06-20 14:00:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:18", "window_end": "14:00", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:48", "window_end": "14:00", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:01", "datetime": "2025-06-20 14:01:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:19", "window_end": "14:01", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:49", "window_end": "14:01", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:02", "datetime": "2025-06-20 14:02:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:20", "window_end": "14:02", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "12:50", "window_end": "14:02", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "14:03", "datetime": "2025-06-20 14:03:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:21", "window_end": "14:03", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:51", "window_end": "14:03", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:04", "datetime": "2025-06-20 14:04:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:22", "window_end": "14:04", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:52", "window_end": "14:04", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:05", "datetime": "2025-06-20 14:05:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:23", "window_end": "14:05", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:53", "window_end": "14:05", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:06", "datetime": "2025-06-20 14:06:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:24", "window_end": "14:06", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:54", "window_end": "14:06", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:07", "datetime": "2025-06-20 14:07:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:25", "window_end": "14:07", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "12:55", "window_end": "14:07", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "14:08", "datetime": "2025-06-20 14:08:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:26", "window_end": "14:08", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:56", "window_end": "14:08", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:09", "datetime": "2025-06-20 14:09:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:27", "window_end": "14:09", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:57", "window_end": "14:09", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:10", "datetime": "2025-06-20 14:10:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:28", "window_end": "14:10", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:58", "window_end": "14:10", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:11", "datetime": "2025-06-20 14:11:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:29", "window_end": "14:11", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "12:59", "window_end": "14:11", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:12", "datetime": "2025-06-20 14:12:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:30", "window_end": "14:12", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:00", "window_end": "14:12", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:13", "datetime": "2025-06-20 14:13:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:31", "window_end": "14:13", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:01", "window_end": "14:13", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:14", "datetime": "2025-06-20 14:14:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:32", "window_end": "14:14", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:02", "window_end": "14:14", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:15", "datetime": "2025-06-20 14:15:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:33", "window_end": "14:15", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:03", "window_end": "14:15", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:16", "datetime": "2025-06-20 14:16:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:34", "window_end": "14:16", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:04", "window_end": "14:16", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "14:17", "datetime": "2025-06-20 14:17:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:35", "window_end": "14:17", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:05", "window_end": "14:17", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:18", "datetime": "2025-06-20 14:18:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:36", "window_end": "14:18", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:06", "window_end": "14:18", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:19", "datetime": "2025-06-20 14:19:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:37", "window_end": "14:19", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:07", "window_end": "14:19", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:20", "datetime": "2025-06-20 14:20:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:38", "window_end": "14:20", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:08", "window_end": "14:20", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:21", "datetime": "2025-06-20 14:21:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:39", "window_end": "14:21", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:09", "window_end": "14:21", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:22", "datetime": "2025-06-20 14:22:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:40", "window_end": "14:22", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:10", "window_end": "14:22", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:23", "datetime": "2025-06-20 14:23:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:41", "window_end": "14:23", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:11", "window_end": "14:23", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "14:24", "datetime": "2025-06-20 14:24:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:42", "window_end": "14:24", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:12", "window_end": "14:24", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:25", "datetime": "2025-06-20 14:25:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "13:43", "window_end": "14:25", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "13:13", "window_end": "14:25", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "14:26", "datetime": "2025-06-20 14:26:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:44", "window_end": "14:26", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:14", "window_end": "14:26", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:27", "datetime": "2025-06-20 14:27:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:45", "window_end": "14:27", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:15", "window_end": "14:27", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:28", "datetime": "2025-06-20 14:28:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:46", "window_end": "14:28", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:16", "window_end": "14:28", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:29", "datetime": "2025-06-20 14:29:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:47", "window_end": "14:29", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:17", "window_end": "14:29", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:30", "datetime": "2025-06-20 14:30:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:48", "window_end": "14:30", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "13:18", "window_end": "14:30", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "14:31", "datetime": "2025-06-20 14:31:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:49", "window_end": "14:31", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:19", "window_end": "14:31", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:32", "datetime": "2025-06-20 14:32:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:50", "window_end": "14:32", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:20", "window_end": "14:32", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:33", "datetime": "2025-06-20 14:33:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:51", "window_end": "14:33", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:21", "window_end": "14:33", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:34", "datetime": "2025-06-20 14:34:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:52", "window_end": "14:34", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "13:22", "window_end": "14:34", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "14:35", "datetime": "2025-06-20 14:35:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:53", "window_end": "14:35", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:23", "window_end": "14:35", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:36", "datetime": "2025-06-20 14:36:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:54", "window_end": "14:36", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:24", "window_end": "14:36", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "14:37", "datetime": "2025-06-20 14:37:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:55", "window_end": "14:37", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:25", "window_end": "14:37", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:38", "datetime": "2025-06-20 14:38:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:56", "window_end": "14:38", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:26", "window_end": "14:38", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:39", "datetime": "2025-06-20 14:39:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:57", "window_end": "14:39", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:27", "window_end": "14:39", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:40", "datetime": "2025-06-20 14:40:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:58", "window_end": "14:40", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:28", "window_end": "14:40", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "14:41", "datetime": "2025-06-20 14:41:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:59", "window_end": "14:41", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:29", "window_end": "14:41", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:42", "datetime": "2025-06-20 14:42:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:00", "window_end": "14:42", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:30", "window_end": "14:42", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:43", "datetime": "2025-06-20 14:43:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:01", "window_end": "14:43", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:31", "window_end": "14:43", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:44", "datetime": "2025-06-20 14:44:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:02", "window_end": "14:44", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:32", "window_end": "14:44", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:45", "datetime": "2025-06-20 14:45:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:03", "window_end": "14:45", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:33", "window_end": "14:45", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "14:46", "datetime": "2025-06-20 14:46:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:04", "window_end": "14:46", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:34", "window_end": "14:46", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "14:47", "datetime": "2025-06-20 14:47:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:05", "window_end": "14:47", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:35", "window_end": "14:47", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "14:48", "datetime": "2025-06-20 14:48:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:06", "window_end": "14:48", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:36", "window_end": "14:48", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:49", "datetime": "2025-06-20 14:49:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "14:07", "window_end": "14:49", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "13:37", "window_end": "14:49", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "14:50", "datetime": "2025-06-20 14:50:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "14:08", "window_end": "14:50", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "13:38", "window_end": "14:50", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "14:51", "datetime": "2025-06-20 14:51:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:09", "window_end": "14:51", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:39", "window_end": "14:51", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:52", "datetime": "2025-06-20 14:52:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:10", "window_end": "14:52", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:40", "window_end": "14:52", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:53", "datetime": "2025-06-20 14:53:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:11", "window_end": "14:53", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:41", "window_end": "14:53", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:54", "datetime": "2025-06-20 14:54:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:12", "window_end": "14:54", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:42", "window_end": "14:54", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:55", "datetime": "2025-06-20 14:55:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:13", "window_end": "14:55", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:43", "window_end": "14:55", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:56", "datetime": "2025-06-20 14:56:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:14", "window_end": "14:56", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:44", "window_end": "14:56", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:57", "datetime": "2025-06-20 14:57:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:15", "window_end": "14:57", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:45", "window_end": "14:57", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "14:58", "datetime": "2025-06-20 14:58:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:16", "window_end": "14:58", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "13:46", "window_end": "14:58", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: PASS", "signal_type": "NONE"}, {"time": "14:59", "datetime": "2025-06-20 14:59:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:17", "window_end": "14:59", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:47", "window_end": "14:59", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:00", "datetime": "2025-06-20 15:00:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:18", "window_end": "15:00", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:48", "window_end": "15:00", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:01", "datetime": "2025-06-20 15:01:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:19", "window_end": "15:01", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:49", "window_end": "15:01", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:02", "datetime": "2025-06-20 15:02:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:20", "window_end": "15:02", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:50", "window_end": "15:02", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:03", "datetime": "2025-06-20 15:03:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:21", "window_end": "15:03", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:51", "window_end": "15:03", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:04", "datetime": "2025-06-20 15:04:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:22", "window_end": "15:04", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:52", "window_end": "15:04", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:05", "datetime": "2025-06-20 15:05:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:23", "window_end": "15:05", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:53", "window_end": "15:05", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:06", "datetime": "2025-06-20 15:06:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:24", "window_end": "15:06", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:54", "window_end": "15:06", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:07", "datetime": "2025-06-20 15:07:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:25", "window_end": "15:07", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:55", "window_end": "15:07", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:08", "datetime": "2025-06-20 15:08:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:26", "window_end": "15:08", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:56", "window_end": "15:08", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:09", "datetime": "2025-06-20 15:09:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:27", "window_end": "15:09", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:57", "window_end": "15:09", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:10", "datetime": "2025-06-20 15:10:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:28", "window_end": "15:10", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:58", "window_end": "15:10", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:11", "datetime": "2025-06-20 15:11:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:29", "window_end": "15:11", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "13:59", "window_end": "15:11", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:12", "datetime": "2025-06-20 15:12:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:30", "window_end": "15:12", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:00", "window_end": "15:12", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "15:13", "datetime": "2025-06-20 15:13:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:31", "window_end": "15:13", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:01", "window_end": "15:13", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:14", "datetime": "2025-06-20 15:14:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:32", "window_end": "15:14", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:02", "window_end": "15:14", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:15", "datetime": "2025-06-20 15:15:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:33", "window_end": "15:15", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:03", "window_end": "15:15", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:16", "datetime": "2025-06-20 15:16:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:34", "window_end": "15:16", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:04", "window_end": "15:16", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:17", "datetime": "2025-06-20 15:17:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:35", "window_end": "15:17", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:05", "window_end": "15:17", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:18", "datetime": "2025-06-20 15:18:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:36", "window_end": "15:18", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:06", "window_end": "15:18", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:19", "datetime": "2025-06-20 15:19:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:37", "window_end": "15:19", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:07", "window_end": "15:19", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:20", "datetime": "2025-06-20 15:20:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:38", "window_end": "15:20", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:08", "window_end": "15:20", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "15:21", "datetime": "2025-06-20 15:21:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:39", "window_end": "15:21", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:09", "window_end": "15:21", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "15:22", "datetime": "2025-06-20 15:22:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:40", "window_end": "15:22", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:10", "window_end": "15:22", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "15:23", "datetime": "2025-06-20 15:23:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:41", "window_end": "15:23", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:11", "window_end": "15:23", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:24", "datetime": "2025-06-20 15:24:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:42", "window_end": "15:24", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:12", "window_end": "15:24", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:25", "datetime": "2025-06-20 15:25:00", "stage1": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:43", "window_end": "15:25", "data_points": 43}, "stage2": {"pass": "False", "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: 0.0", "window_start": "14:13", "window_end": "15:25", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}, {"time": "15:26", "datetime": "2025-06-20 15:26:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "14:44", "window_end": "15:26", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "14:14", "window_end": "15:26", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "15:27", "datetime": "2025-06-20 15:27:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "14:45", "window_end": "15:27", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "14:15", "window_end": "15:27", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "15:28", "datetime": "2025-06-20 15:28:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "14:46", "window_end": "15:28", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: True, Nadarya: 1.0", "window_start": "14:16", "window_end": "15:28", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "15:29", "datetime": "2025-06-20 15:29:00", "stage1": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:47", "window_end": "15:29", "data_points": 43}, "stage2": {"pass": "True", "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: True, Nadarya: -1.0", "window_start": "14:17", "window_end": "15:29", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "15:30", "datetime": "2025-06-20 15:30:00", "stage1": {"pass": "False", "sideways": "False", "nadarya_signal": 0, "reason": "Sideways: F<PERSON>e, Nadarya: 0.0", "window_start": "14:48", "window_end": "15:30", "data_points": 42}, "stage2": {"pass": "False", "sideways": "False", "nadarya_signal": 0, "reason": "Sideways: F<PERSON>e, Nadarya: 0.0", "window_start": "14:18", "window_end": "15:30", "data_points": 72}, "final_signal": 0, "signal_reason": "Stage1: FAIL, Stage2: FAIL", "signal_type": "NONE"}], "position_events": [{"type": "ENTRY", "time": "12:01", "datetime": "2025-06-20 12:01:00", "position_type": "PUT", "signal_strength": 1, "entry_reason": "Both stages agree: PUT", "stage1_pass": "True", "stage2_pass": "True"}, {"type": "EXIT", "time": "14:01", "datetime": "2025-06-20 14:01:00", "position_type": "PUT", "entry_time": "12:01", "duration_minutes": 120, "exit_reason": "Maximum duration reached (2 hours)", "exit_method": "DURATION_BASED"}, {"type": "ENTRY", "time": "14:02", "datetime": "2025-06-20 14:02:00", "position_type": "PUT", "signal_strength": 1, "entry_reason": "Both stages agree: PUT", "stage1_pass": "True", "stage2_pass": "True"}, {"type": "EXIT", "time": "15:00", "datetime": "2025-06-20 15:00:00", "position_type": "PUT", "entry_time": "14:02", "duration_minutes": 58, "exit_reason": "Session end reached", "exit_method": "TIME_BASED"}, {"type": "ENTRY", "time": "15:12", "datetime": "2025-06-20 15:12:00", "position_type": "PUT", "signal_strength": 1, "entry_reason": "Both stages agree: PUT", "stage1_pass": "True", "stage2_pass": "True"}, {"type": "EXIT", "time": "15:13", "datetime": "2025-06-20 15:13:00", "position_type": "PUT", "entry_time": "15:12", "duration_minutes": 1, "exit_reason": "Session end reached", "exit_method": "TIME_BASED"}, {"type": "ENTRY", "time": "15:20", "datetime": "2025-06-20 15:20:00", "position_type": "PUT", "signal_strength": 1, "entry_reason": "Both stages agree: PUT", "stage1_pass": "True", "stage2_pass": "True"}, {"type": "EXIT", "time": "15:21", "datetime": "2025-06-20 15:21:00", "position_type": "PUT", "entry_time": "15:20", "duration_minutes": 1, "exit_reason": "Session end reached", "exit_method": "TIME_BASED"}, {"type": "ENTRY", "time": "15:21", "datetime": "2025-06-20 15:21:00", "position_type": "PUT", "signal_strength": 1, "entry_reason": "Both stages agree: PUT", "stage1_pass": "True", "stage2_pass": "True"}, {"type": "EXIT", "time": "15:22", "datetime": "2025-06-20 15:22:00", "position_type": "PUT", "entry_time": "15:21", "duration_minutes": 1, "exit_reason": "Session end reached", "exit_method": "TIME_BASED"}, {"type": "ENTRY", "time": "15:22", "datetime": "2025-06-20 15:22:00", "position_type": "PUT", "signal_strength": 1, "entry_reason": "Both stages agree: PUT", "stage1_pass": "True", "stage2_pass": "True"}, {"type": "EXIT", "time": "15:23", "datetime": "2025-06-20 15:23:00", "position_type": "PUT", "entry_time": "15:22", "duration_minutes": 1, "exit_reason": "Session end reached", "exit_method": "TIME_BASED"}, {"type": "ENTRY", "time": "15:26", "datetime": "2025-06-20 15:26:00", "position_type": "CALL", "signal_strength": 1, "entry_reason": "Both stages agree: CALL", "stage1_pass": "True", "stage2_pass": "True"}, {"type": "EXIT", "time": "15:27", "datetime": "2025-06-20 15:27:00", "position_type": "CALL", "entry_time": "15:26", "duration_minutes": 1, "exit_reason": "Session end reached", "exit_method": "TIME_BASED"}, {"type": "ENTRY", "time": "15:27", "datetime": "2025-06-20 15:27:00", "position_type": "CALL", "signal_strength": 1, "entry_reason": "Both stages agree: CALL", "stage1_pass": "True", "stage2_pass": "True"}, {"type": "EXIT", "time": "15:28", "datetime": "2025-06-20 15:28:00", "position_type": "CALL", "entry_time": "15:27", "duration_minutes": 1, "exit_reason": "Session end reached", "exit_method": "TIME_BASED"}, {"type": "ENTRY", "time": "15:28", "datetime": "2025-06-20 15:28:00", "position_type": "CALL", "signal_strength": 1, "entry_reason": "Both stages agree: CALL", "stage1_pass": "True", "stage2_pass": "True"}, {"type": "EXIT", "time": "15:29", "datetime": "2025-06-20 15:29:00", "position_type": "CALL", "entry_time": "15:28", "duration_minutes": 1, "exit_reason": "Session end reached", "exit_method": "TIME_BASED"}, {"type": "ENTRY", "time": "15:29", "datetime": "2025-06-20 15:29:00", "position_type": "PUT", "signal_strength": 1, "entry_reason": "Both stages agree: PUT", "stage1_pass": "True", "stage2_pass": "True"}, {"type": "EXIT", "time": "15:30", "datetime": "2025-06-20 15:30:00", "position_type": "PUT", "entry_time": "15:29", "duration_minutes": 1, "exit_reason": "Session end reached", "exit_method": "TIME_BASED"}], "data_summary": {"total_candles": 301, "data_range": "2025-06-20 10:29:00 to 2025-06-20 15:29:00", "analysis_minutes": 211}}