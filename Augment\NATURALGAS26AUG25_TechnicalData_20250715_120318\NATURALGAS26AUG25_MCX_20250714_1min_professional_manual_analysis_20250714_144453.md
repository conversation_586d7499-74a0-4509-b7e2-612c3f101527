# 💼 PROFESSIONAL MANUAL SIGNAL ANALYSIS REPORT (OUTLIER-FILTERED)

**Analysis Date:** 2025-07-14 14:44:53
**Total True Signals Found:** 24
**Indicators Analyzed:** 8
**Outlier Threshold:** 25.0% deviation from professional values

## 📊 SUMMARY OF FINDINGS

- **BUY Signals:** 14
- **SELL Signals:** 10
- **Average Profit:** 1.47%
- **Maximum Profit:** 3.22%
- **Minimum Profit:** 0.52%

## 🎯 ACTUAL INDICATOR THRESHOLDS (MARKET-BASED, OUTLIER-FILTERED)

These thresholds are extracted from REAL profitable moments after filtering outliers:

### PGO_14
**Professional Reference Values:**
- detection_oversold: -3.2000 (industry standard)
- confirmation_oversold: -2.4000 (industry standard)
- detection_overbought: 3.2000 (industry standard)
- confirmation_overbought: 2.4000 (industry standard)

**Market-Based Values (after outlier filtering):**
- **detection_oversold:** -0.9216 (deviation from professional: -71.2%)
- **confirmation_oversold:** -0.4343 (deviation from professional: -81.9%)
- **detection_overbought:** 0.0531 (deviation from professional: -98.3%)
- **confirmation_overbought:** -0.4343 (deviation from professional: -118.1%)

### CCI_14
**Professional Reference Values:**
- detection_oversold: -100.0000 (industry standard)
- confirmation_oversold: -80.0000 (industry standard)
- detection_overbought: 100.0000 (industry standard)
- confirmation_overbought: 80.0000 (industry standard)

**Market-Based Values (after outlier filtering):**
- **detection_oversold:** -82.4742 (deviation from professional: -17.5%)
- **confirmation_oversold:** -82.4742 (deviation from professional: +3.1%)
- **detection_overbought:** 78.2935 (deviation from professional: -21.7%)
- **confirmation_overbought:** 78.2935 (deviation from professional: -2.1%)

### SMI_5_20_5_SMIo_5_20_5_100.0
**Professional Reference Values:**
- detection_oversold: -40.0000 (industry standard)
- confirmation_oversold: -30.0000 (industry standard)
- detection_overbought: 40.0000 (industry standard)
- confirmation_overbought: 30.0000 (industry standard)

**Market-Based Values (after outlier filtering):**
- **detection_oversold:** -8.4731 (deviation from professional: -78.8%)
- **confirmation_oversold:** -8.4731 (deviation from professional: -71.8%)
- **detection_overbought:** -8.4731 (deviation from professional: -121.2%)
- **confirmation_overbought:** -8.4731 (deviation from professional: -128.2%)

### BIAS_26
**Professional Reference Values:**
- detection_oversold: -8.0000 (industry standard)
- confirmation_oversold: -6.0000 (industry standard)
- detection_overbought: 8.0000 (industry standard)
- confirmation_overbought: 6.0000 (industry standard)

**Market-Based Values (after outlier filtering):**
- **detection_oversold:** -8.0000 (deviation from professional: -0.0%)
- **confirmation_oversold:** -6.0000 (deviation from professional: -0.0%)
- **detection_overbought:** 8.0000 (deviation from professional: +0.0%)
- **confirmation_overbought:** 6.0000 (deviation from professional: +0.0%)

### CG_10
**Professional Reference Values:**
- detection_oversold: -0.0500 (industry standard)
- confirmation_oversold: -0.0300 (industry standard)
- detection_overbought: 0.0500 (industry standard)
- confirmation_overbought: 0.0300 (industry standard)

**Market-Based Values (after outlier filtering):**
- **detection_oversold:** -5.5014 (deviation from professional: +10902.7%)
- **confirmation_oversold:** -5.4996 (deviation from professional: +18232.0%)
- **detection_overbought:** -5.4972 (deviation from professional: -11094.3%)
- **confirmation_overbought:** -5.4996 (deviation from professional: -18432.0%)

### ACCBANDS_10_ACCBU_10
**Professional Reference Values:**
- detection_oversold: 300.0000 (industry standard)
- confirmation_oversold: 302.0000 (industry standard)
- detection_overbought: 310.0000 (industry standard)
- confirmation_overbought: 308.0000 (industry standard)

**Market-Based Values (after outlier filtering):**
- **detection_oversold:** 306.8149 (deviation from professional: +2.3%)
- **confirmation_oversold:** 306.9528 (deviation from professional: +1.6%)
- **detection_overbought:** 308.7744 (deviation from professional: -0.4%)
- **confirmation_overbought:** 308.2968 (deviation from professional: +0.1%)

### QQE_14_QQE_14_5_4.236_RSIMA
**Professional Reference Values:**
- detection_oversold: 20.0000 (industry standard)
- confirmation_oversold: 30.0000 (industry standard)
- detection_overbought: 80.0000 (industry standard)
- confirmation_overbought: 70.0000 (industry standard)

**Market-Based Values (after outlier filtering):**
- **detection_overbought:** 61.8553 (deviation from professional: -22.7%)
- **confirmation_overbought:** 61.8553 (deviation from professional: -11.6%)

### SMI_5_20_5_SMI_5_20_5_100.0
**Professional Reference Values:**
- detection_oversold: -40.0000 (industry standard)
- confirmation_oversold: -30.0000 (industry standard)
- detection_overbought: 40.0000 (industry standard)
- confirmation_overbought: 30.0000 (industry standard)

**Market-Based Values (after outlier filtering):**
- **detection_overbought:** 34.6406 (deviation from professional: -13.4%)
- **confirmation_overbought:** 34.6406 (deviation from professional: +15.5%)

## 📈 DETAILED SIGNAL ANALYSIS

### Signal 1: BUY
- **Time:** 13:00
- **Entry Price:** 304.20
- **Exit Price:** 305.90
- **Profit:** 0.56%
- **Time to Profit:** 13 minutes
- **Signal Strength:** 0.56

### Signal 2: SELL
- **Time:** 14:15
- **Entry Price:** 305.40
- **Exit Price:** 303.80
- **Profit:** 0.52%
- **Time to Profit:** 3 minutes
- **Signal Strength:** 0.52

### Signal 3: BUY
- **Time:** 15:00
- **Entry Price:** 303.80
- **Exit Price:** 305.90
- **Profit:** 0.69%
- **Time to Profit:** 3 minutes
- **Signal Strength:** 0.69

### Signal 4: BUY
- **Time:** 15:15
- **Entry Price:** 304.20
- **Exit Price:** 305.90
- **Profit:** 0.56%
- **Time to Profit:** 5 minutes
- **Signal Strength:** 0.56

### Signal 5: BUY
- **Time:** 15:30
- **Entry Price:** 304.20
- **Exit Price:** 307.50
- **Profit:** 1.08%
- **Time to Profit:** 4 minutes
- **Signal Strength:** 1.08

### Signal 6: BUY
- **Time:** 15:45
- **Entry Price:** 305.40
- **Exit Price:** 307.50
- **Profit:** 0.69%
- **Time to Profit:** 14 minutes
- **Signal Strength:** 0.69

### Signal 7: SELL
- **Time:** 15:45
- **Entry Price:** 305.40
- **Exit Price:** 303.80
- **Profit:** 0.52%
- **Time to Profit:** 8 minutes
- **Signal Strength:** 0.52

### Signal 8: BUY
- **Time:** 16:00
- **Entry Price:** 305.00
- **Exit Price:** 307.50
- **Profit:** 0.82%
- **Time to Profit:** 13 minutes
- **Signal Strength:** 0.82

### Signal 9: BUY
- **Time:** 16:15
- **Entry Price:** 305.70
- **Exit Price:** 307.50
- **Profit:** 0.59%
- **Time to Profit:** 12 minutes
- **Signal Strength:** 0.59

### Signal 10: SELL
- **Time:** 16:15
- **Entry Price:** 305.70
- **Exit Price:** 298.50
- **Profit:** 2.36%
- **Time to Profit:** 3 minutes
- **Signal Strength:** 2.36

### Signal 11: BUY
- **Time:** 16:30
- **Entry Price:** 305.80
- **Exit Price:** 307.50
- **Profit:** 0.56%
- **Time to Profit:** 11 minutes
- **Signal Strength:** 0.56

### Signal 12: SELL
- **Time:** 16:30
- **Entry Price:** 305.80
- **Exit Price:** 298.50
- **Profit:** 2.39%
- **Time to Profit:** 2 minutes
- **Signal Strength:** 2.39

### Signal 13: BUY
- **Time:** 16:45
- **Entry Price:** 305.90
- **Exit Price:** 307.50
- **Profit:** 0.52%
- **Time to Profit:** 10 minutes
- **Signal Strength:** 0.52

### Signal 14: SELL
- **Time:** 16:45
- **Entry Price:** 305.90
- **Exit Price:** 297.30
- **Profit:** 2.81%
- **Time to Profit:** 1 minutes
- **Signal Strength:** 2.81

### Signal 15: BUY
- **Time:** 17:00
- **Entry Price:** 304.10
- **Exit Price:** 307.50
- **Profit:** 1.12%
- **Time to Profit:** 5 minutes
- **Signal Strength:** 1.12

### Signal 16: SELL
- **Time:** 17:00
- **Entry Price:** 304.10
- **Exit Price:** 295.00
- **Profit:** 2.99%
- **Time to Profit:** 12 minutes
- **Signal Strength:** 2.99

### Signal 17: BUY
- **Time:** 17:15
- **Entry Price:** 304.70
- **Exit Price:** 307.50
- **Profit:** 0.92%
- **Time to Profit:** 8 minutes
- **Signal Strength:** 0.92

### Signal 18: SELL
- **Time:** 17:15
- **Entry Price:** 304.70
- **Exit Price:** 295.00
- **Profit:** 3.18%
- **Time to Profit:** 11 minutes
- **Signal Strength:** 3.18

### Signal 19: BUY
- **Time:** 17:30
- **Entry Price:** 304.50
- **Exit Price:** 307.50
- **Profit:** 0.99%
- **Time to Profit:** 7 minutes
- **Signal Strength:** 0.99

### Signal 20: SELL
- **Time:** 17:30
- **Entry Price:** 304.50
- **Exit Price:** 295.00
- **Profit:** 3.12%
- **Time to Profit:** 10 minutes
- **Signal Strength:** 3.12

### Signal 21: BUY
- **Time:** 17:45
- **Entry Price:** 303.80
- **Exit Price:** 307.50
- **Profit:** 1.22%
- **Time to Profit:** 2 minutes
- **Signal Strength:** 1.22

### Signal 22: SELL
- **Time:** 17:45
- **Entry Price:** 303.80
- **Exit Price:** 295.00
- **Profit:** 2.90%
- **Time to Profit:** 9 minutes
- **Signal Strength:** 2.90

### Signal 23: BUY
- **Time:** 18:00
- **Entry Price:** 304.80
- **Exit Price:** 307.50
- **Profit:** 0.89%
- **Time to Profit:** 5 minutes
- **Signal Strength:** 0.89

### Signal 24: SELL
- **Time:** 18:00
- **Entry Price:** 304.80
- **Exit Price:** 295.00
- **Profit:** 3.22%
- **Time to Profit:** 8 minutes
- **Signal Strength:** 3.22

## 🔧 RECOMMENDED THRESHOLD UPDATES

Based on this analysis, the following thresholds should be used:

```python
PROFESSIONAL_THRESHOLDS = {
    'PGO_14': {
        'detection_oversold': -0.9216,
        'confirmation_oversold': -0.4343,
        'detection_overbought': 0.0531,
        'confirmation_overbought': -0.4343,
    },
    'CCI_14': {
        'detection_oversold': -82.4742,
        'confirmation_oversold': -82.4742,
        'detection_overbought': 78.2935,
        'confirmation_overbought': 78.2935,
    },
    'SMI_5_20_5_SMIo_5_20_5_100.0': {
        'detection_oversold': -8.4731,
        'confirmation_oversold': -8.4731,
        'detection_overbought': -8.4731,
        'confirmation_overbought': -8.4731,
    },
    'BIAS_26': {
        'detection_oversold': -8.0000,
        'confirmation_oversold': -6.0000,
        'detection_overbought': 8.0000,
        'confirmation_overbought': 6.0000,
    },
    'CG_10': {
        'detection_oversold': -5.5014,
        'confirmation_oversold': -5.4996,
        'detection_overbought': -5.4972,
        'confirmation_overbought': -5.4996,
    },
    'ACCBANDS_10_ACCBU_10': {
        'detection_oversold': 306.8149,
        'confirmation_oversold': 306.9528,
        'detection_overbought': 308.7744,
        'confirmation_overbought': 308.2968,
    },
    'QQE_14_QQE_14_5_4.236_RSIMA': {
        'detection_overbought': 61.8553,
        'confirmation_overbought': 61.8553,
    },
    'SMI_5_20_5_SMI_5_20_5_100.0': {
        'detection_overbought': 34.6406,
        'confirmation_overbought': 34.6406,
    },
}
```

## 📝 PROFESSIONAL INSIGHTS

1. **Market Reality:** These thresholds reflect actual market conditions
2. **Profit Validation:** Each threshold is validated by real profit opportunities
3. **Outlier Filtering:** Signals >25% away from professional values were excluded
4. **Professional Alignment:** Values are compared against industry standards
5. **No Guesswork:** Values are extracted from profitable trading moments
6. **Ready for ML:** These can now be used as starting points for ML optimization

## 🔍 OUTLIER FILTERING METHODOLOGY

Professional traders know that extreme indicator values are often false signals.
This analysis filters out signals where indicator values deviate more than 25%
from established professional trading thresholds, ensuring only realistic
and tradeable signals are used for threshold extraction.

## ⚠️ QUALITY ASSURANCE

- All thresholds are validated against professional trading standards
- Outlier signals are documented but excluded from threshold calculation
- Only signals within realistic trading ranges are used
- Each threshold represents actual profitable trading opportunities
