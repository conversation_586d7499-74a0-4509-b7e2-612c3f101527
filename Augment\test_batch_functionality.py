"""
Test Batch Functionality

Test script to verify that the new multiple intervals and multiple days functionality works correctly.
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_parse_intervals():
    """Test interval parsing functionality"""
    
    logger.info("Testing interval parsing...")
    
    try:
        from integrated_technical_analyzer import parse_intervals
        
        # Test cases
        test_cases = [
            ("1", ["1"]),
            ("1,5,15", ["1", "5", "15"]),
            ("1, 5, 15, 30", ["1", "5", "15", "30"]),
            ("1,invalid,5", ["1", "5"]),  # Should filter out invalid
            ("invalid", ["1"]),  # Should default to 1
            ("", ["1"]),  # Should default to 1
        ]
        
        for input_str, expected in test_cases:
            result = parse_intervals(input_str)
            if result == expected:
                logger.info(f"✅ '{input_str}' → {result}")
            else:
                logger.error(f"❌ '{input_str}' → {result}, expected {expected}")
                return False
        
        logger.info("✅ Interval parsing tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing interval parsing: {e}")
        return False

def test_generate_working_dates():
    """Test working dates generation"""
    
    logger.info("Testing working dates generation...")
    
    try:
        from integrated_technical_analyzer import generate_working_dates
        
        # Test cases (now going backwards from input date)
        test_cases = [
            ("03-07-2025", 1, 1),  # Thursday, should return 1 date (same date)
            ("03-07-2025", 3, 3),  # Thursday, should return 3 working days backwards
            ("07-07-2025", 3, 3),  # Monday, should skip weekend going backwards
            ("08-07-2025", 3, 3),  # Tuesday, should skip weekend going backwards
        ]
        
        for end_date, num_days, expected_count in test_cases:
            result = generate_working_dates(end_date, num_days)
            if len(result) == expected_count:
                logger.info(f"✅ {end_date} - {num_days} days backwards → {len(result)} dates: {result}")
            else:
                logger.error(f"❌ {end_date} - {num_days} days backwards → {len(result)} dates, expected {expected_count}")
                return False

        # Test weekend exclusion going backwards
        weekend_dates = generate_working_dates("08-07-2025", 5)  # End on Tuesday, go back 5 working days
        logger.info(f"Weekend test (backwards): {weekend_dates}")
        
        # Check that no weekends are included
        for date_str in weekend_dates:
            date_obj = datetime.strptime(date_str, '%d-%m-%Y')
            if date_obj.weekday() >= 5:  # Saturday or Sunday
                logger.error(f"❌ Weekend date found: {date_str}")
                return False
        
        logger.info("✅ Working dates generation tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing working dates: {e}")
        return False

def test_create_output_filename():
    """Test output filename creation"""
    
    logger.info("Testing output filename creation...")
    
    try:
        from integrated_technical_analyzer import create_output_filename
        
        # Test cases
        test_cases = [
            ("ACC", "NSE", "03-07-2025", "1", "signals", "extension", None),
            ("NATURALGAS26AUG25", "MCX", "03-07-2025", "5", "signals", "extension", ["pgo", "cci", "cg"]),
            ("BATAINDIA", "BSE", "30-06-2025", "15", "full", "extension", ["rsi", "macd", "bbands", "atr", "sma"]),
        ]
        
        for ticker, exchange, date, interval, analysis_type, method, functions in test_cases:
            filename = create_output_filename(ticker, exchange, date, interval, analysis_type, method, functions)
            
            # Check filename components
            expected_parts = [ticker, exchange, date.replace('-', ''), f"{interval}min", analysis_type, method]
            if functions:
                expected_parts.append("funcs")
            
            all_parts_found = all(part in filename for part in expected_parts)
            
            if all_parts_found and filename.endswith('.xlsx'):
                logger.info(f"✅ {ticker}_{exchange} → {filename}")
            else:
                logger.error(f"❌ {ticker}_{exchange} → {filename} (missing parts or wrong extension)")
                return False
        
        logger.info("✅ Output filename creation tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing filename creation: {e}")
        return False

def test_cli_parsing():
    """Test CLI parsing with new parameters"""
    
    logger.info("Testing CLI parsing...")
    
    try:
        from integrated_technical_analyzer import create_cli_parser
        
        parser = create_cli_parser()
        
        # Test multiple intervals
        test_args_1 = [
            '--mode', 'analysis',
            '--analysis-type', 'signals',
            '--ticker', 'ACC',
            '--exchange', 'NSE',
            '--date', '03-07-2025',
            '--method', 'extension',
            '--functions', 'rsi,macd,bbands',
            '--interval', '1,5,15',
            '--days', '3'
        ]
        
        args = parser.parse_args(test_args_1)
        
        logger.info(f"Parsed arguments:")
        logger.info(f"  interval: {args.interval}")
        logger.info(f"  days: {args.days}")
        logger.info(f"  functions: {args.functions}")
        
        if args.interval == '1,5,15' and args.days == 3 and args.functions == 'rsi,macd,bbands':
            logger.info("✅ CLI parsing test passed!")
            return True
        else:
            logger.error("❌ CLI parsing test failed!")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error testing CLI parsing: {e}")
        return False

def test_batch_logic_simulation():
    """Test batch processing logic simulation"""
    
    logger.info("Testing batch processing logic simulation...")
    
    try:
        from integrated_technical_analyzer import parse_intervals, generate_working_dates
        
        # Simulate batch processing parameters
        intervals = parse_intervals("1,5,15")
        dates = generate_working_dates("03-07-2025", 3)  # Now goes backwards

        total_combinations = len(intervals) * len(dates)

        logger.info(f"Batch simulation (backwards dates):")
        logger.info(f"  Intervals: {intervals}")
        logger.info(f"  Dates: {dates} (backwards from 03-07-2025)")
        logger.info(f"  Total combinations: {total_combinations}")
        
        # Simulate processing
        combinations = []
        for date in dates:
            for interval in intervals:
                combinations.append(f"{date}_{interval}min")
        
        logger.info(f"  Combinations: {combinations}")
        
        if len(combinations) == total_combinations:
            logger.info("✅ Batch logic simulation passed!")
            return True
        else:
            logger.error("❌ Batch logic simulation failed!")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error in batch logic simulation: {e}")
        return False

def main():
    """Main test function"""
    
    logger.info("🚀 Starting Batch Functionality Tests...")
    
    # Test 1: Interval parsing
    logger.info("\n" + "="*60)
    logger.info("TEST 1: INTERVAL PARSING")
    logger.info("="*60)
    test1 = test_parse_intervals()
    
    # Test 2: Working dates generation
    logger.info("\n" + "="*60)
    logger.info("TEST 2: WORKING DATES GENERATION")
    logger.info("="*60)
    test2 = test_generate_working_dates()
    
    # Test 3: Output filename creation
    logger.info("\n" + "="*60)
    logger.info("TEST 3: OUTPUT FILENAME CREATION")
    logger.info("="*60)
    test3 = test_create_output_filename()
    
    # Test 4: CLI parsing
    logger.info("\n" + "="*60)
    logger.info("TEST 4: CLI PARSING")
    logger.info("="*60)
    test4 = test_cli_parsing()
    
    # Test 5: Batch logic simulation
    logger.info("\n" + "="*60)
    logger.info("TEST 5: BATCH LOGIC SIMULATION")
    logger.info("="*60)
    test5 = test_batch_logic_simulation()
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    tests = {
        'Interval Parsing': test1,
        'Working Dates Generation': test2,
        'Output Filename Creation': test3,
        'CLI Parsing': test4,
        'Batch Logic Simulation': test5
    }
    
    passed = sum(1 for success in tests.values() if success)
    total = len(tests)
    
    logger.info(f"Tests passed: {passed}/{total}")
    
    for test_name, success in tests.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    if passed == total:
        logger.info("🎉 All batch functionality tests passed!")
        logger.info("You can now use:")
        logger.info("  --interval 1,5,15,30  (multiple intervals)")
        logger.info("  --days 5              (5 working days)")
        logger.info("  Both together for batch processing!")
    else:
        logger.info("❌ Some tests failed. Check the implementation.")
    
    logger.info("Tests completed!")

if __name__ == "__main__":
    main()
