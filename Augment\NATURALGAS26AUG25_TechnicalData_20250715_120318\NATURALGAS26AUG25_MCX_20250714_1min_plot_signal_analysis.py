"""
Comprehensive Signal Analysis and Plotting System for Ver4 and Ver6 Comparison

This module creates detailed plots showing:
1. Minute-by-minute signal detection as in real market
2. Entry/exit points with reasons
3. Sideways detection periods
4. Nadarya Watson envelope signals
5. Position management and P&L tracking
6. Comparison between Ver4 and Ver6 implementations
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SignalAnalyzer:
    """Comprehensive signal analysis and plotting system"""
    
    def __init__(self, ticker, date, start_time, end_time, exchange='NSE'):
        self.ticker = ticker
        self.date = date
        self.start_time = start_time
        self.end_time = end_time
        self.exchange = exchange
        self.tokenid = None
        
        # Initialize data storage
        self.minute_data = []
        self.signal_history = []
        self.position_history = []
        self.pnl_history = []
        
        # Import shared components
        from shared_api_manager import get_api
        from shared_nadarya_watson_signal import check_vander
        from shared_sideways_signal_helper import check_sideways
        
        self.api = get_api()
        self.check_vander = check_vander
        self.check_sideways = check_sideways
        
        # Resolve ticker to token
        self._resolve_token()
    
    def _resolve_token(self):
        """Resolve ticker to token ID"""
        try:
            if self.exchange == 'NSE':
                search_result = self.api.searchscrip(exchange='NSE', searchtext=self.ticker + '-EQ')
                if search_result and 'values' in search_result and search_result['values']:
                    self.tokenid = search_result['values'][0]['token']
                    logger.info(f"📊 Resolved {self.ticker} to token: {self.tokenid}")
                else:
                    raise Exception(f"Symbol {self.ticker} not found")
            else:
                raise Exception(f"Exchange {self.exchange} not supported yet")
        except Exception as e:
            logger.error(f"❌ Error resolving token: {str(e)}")
            raise
    
    def get_minute_data(self, start_minute, end_minute):
        """Get minute-by-minute data for analysis"""
        try:
            from optimized_backtester_v4_logic import OptimizedBacktesterV4Logic
            
            # Create backtester instance
            backtester = OptimizedBacktesterV4Logic(
                ticker=self.ticker,
                exchange=self.exchange,
                tokenid=self.tokenid,
                date=self.date,
                start_time=start_minute,
                end_time=end_minute
            )
            
            # Get the underlying data
            data = backtester.get_underlying_data()
            return data
            
        except Exception as e:
            logger.error(f"❌ Error getting minute data: {str(e)}")
            return None
    
    def analyze_minute_by_minute(self):
        """Analyze signals minute by minute as in real market"""
        logger.info(f"🔍 Starting minute-by-minute analysis for {self.ticker}")
        
        # Market hours simulation (12:00 to 15:30 as in Ver4)
        market_start = datetime.strptime('12:00', '%H:%M')
        market_end = datetime.strptime('15:30', '%H:%M')
        current_time = market_start
        
        # Position tracking
        current_position = None
        position_entry_time = None
        position_entry_price = None
        position_type = None
        
        while current_time <= market_end:
            minute_str = current_time.strftime('%H:%M')
            logger.info(f"⏰ Analyzing minute: {minute_str}")
            
            try:
                # Calculate window times (as in Ver4 logic)
                window_07h = timedelta(hours=0.7)
                window_12h = timedelta(hours=1.2)
                
                # For 0.7 hour window
                start_07h = max(current_time - window_07h, market_start)
                end_07h = current_time
                
                # For 1.2 hour window  
                start_12h = max(current_time - window_12h, market_start)
                end_12h = current_time
                
                # Check if we have enough data (minimum 0.7 hours)
                if (current_time - market_start).total_seconds() < 0.7 * 3600:
                    logger.debug(f"⏭️ Skipping {minute_str} - insufficient data")
                    current_time += timedelta(minutes=1)
                    continue
                
                # Skip if we already have a position
                if current_position is not None:
                    logger.debug(f"📍 Position active, skipping signal check at {minute_str}")
                    
                    # Check exit conditions here (simplified for now)
                    # In real implementation, this would call the stop_loss_strategy
                    
                    current_time += timedelta(minutes=1)
                    continue
                
                # Stage 1: Check 0.7 hour sideways + Nadarya
                logger.debug(f"🔍 Stage 1: Checking 0.7h window {start_07h.strftime('%H:%M')}-{end_07h.strftime('%H:%M')}")
                
                # Get sideways signal for 0.7h
                issideways1, sideways_text1 = self.check_sideways(
                    tokenid=self.tokenid,
                    exchange=self.exchange,
                    date_input=self.date,
                    starttime_input=start_07h.strftime('%H:%M'),
                    endtime_input=end_07h.strftime('%H:%M')
                )
                
                # Get Nadarya signal for 0.7h
                isvander1, vander_text1 = self.check_vander(
                    tokenid=self.tokenid,
                    exchange=self.exchange,
                    date_input=self.date,
                    starttime_input=start_07h.strftime('%H:%M'),
                    endtime_input=end_07h.strftime('%H:%M')
                )
                
                logger.debug(f"📊 Stage 1 - Sideways: {issideways1}, Nadarya: {isvander1}")
                
                # Stage 1 must pass for Stage 2
                if not (issideways1 and isvander1):
                    logger.debug(f"❌ Stage 1 failed at {minute_str}")
                    
                    # Record the analysis
                    self.minute_data.append({
                        'time': minute_str,
                        'stage1_sideways': issideways1,
                        'stage1_nadarya': isvander1,
                        'stage2_sideways': False,
                        'stage2_nadarya': False,
                        'signal': 0,
                        'signal_text': 'Stage 1 failed',
                        'position': None
                    })
                    
                    current_time += timedelta(minutes=1)
                    continue
                
                # Stage 2: Check 1.2 hour sideways + Nadarya
                logger.debug(f"🔍 Stage 2: Checking 1.2h window {start_12h.strftime('%H:%M')}-{end_12h.strftime('%H:%M')}")
                
                # Get sideways signal for 1.2h
                issideways2, sideways_text2 = self.check_sideways(
                    tokenid=self.tokenid,
                    exchange=self.exchange,
                    date_input=self.date,
                    starttime_input=start_12h.strftime('%H:%M'),
                    endtime_input=end_12h.strftime('%H:%M')
                )
                
                # Get Nadarya signal for 1.2h
                isvander2, vander_text2 = self.check_vander(
                    tokenid=self.tokenid,
                    exchange=self.exchange,
                    date_input=self.date,
                    starttime_input=start_12h.strftime('%H:%M'),
                    endtime_input=end_12h.strftime('%H:%M')
                )
                
                logger.debug(f"📊 Stage 2 - Sideways: {issideways2}, Nadarya: {isvander2}")
                
                # Determine signal based on Ver4 logic
                signal = 0
                signal_text = "No signal"
                
                if issideways2 and isvander2:
                    if 'Lower band signal present' in vander_text2:
                        signal = 1  # Call signal
                        signal_text = "Call signal - Lower band"
                        logger.info(f"🟢 CALL SIGNAL at {minute_str}")
                    elif 'Upper band signal present' in vander_text2:
                        signal = -1  # Put signal
                        signal_text = "Put signal - Upper band"
                        logger.info(f"🔴 PUT SIGNAL at {minute_str}")
                    else:
                        signal_text = "Stage 2 passed but no band signal"
                else:
                    signal_text = "Stage 2 failed"
                
                # Record the analysis
                analysis_record = {
                    'time': minute_str,
                    'stage1_sideways': issideways1,
                    'stage1_nadarya': isvander1,
                    'stage2_sideways': issideways2,
                    'stage2_nadarya': isvander2,
                    'signal': signal,
                    'signal_text': signal_text,
                    'vander_text1': vander_text1,
                    'vander_text2': vander_text2,
                    'sideways_text1': sideways_text1,
                    'sideways_text2': sideways_text2,
                    'position': current_position
                }
                
                self.minute_data.append(analysis_record)
                
                # If we have a signal, simulate position opening
                if signal != 0:
                    current_position = 'CALL' if signal == 1 else 'PUT'
                    position_entry_time = minute_str
                    position_type = signal
                    
                    logger.info(f"📍 Position opened: {current_position} at {minute_str}")
                    
                    # In real implementation, this would:
                    # 1. Get option data
                    # 2. Calculate entry price
                    # 3. Start monitoring exit conditions
                    
                    # For simulation, we'll close position after some time
                    # This should be replaced with actual stop_loss_strategy logic
                
            except Exception as e:
                logger.error(f"❌ Error analyzing minute {minute_str}: {str(e)}")
                
            current_time += timedelta(minutes=1)
        
        logger.info(f"✅ Completed minute-by-minute analysis. Processed {len(self.minute_data)} minutes")
        return self.minute_data
    
    def create_comprehensive_plot(self):
        """Create comprehensive plots showing signal analysis"""
        if not self.minute_data:
            logger.error("❌ No data to plot. Run analyze_minute_by_minute() first.")
            return
        
        # Convert to DataFrame for easier plotting
        df = pd.DataFrame(self.minute_data)
        df['time'] = pd.to_datetime(df['time'], format='%H:%M')
        
        # Create figure with subplots
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        fig.suptitle(f'Signal Analysis for {self.ticker} on {self.date}', fontsize=16, fontweight='bold')
        
        # Plot 1: Stage 1 and Stage 2 Conditions
        ax1 = axes[0]
        ax1.plot(df['time'], df['stage1_sideways'].astype(int), 'b-', label='Stage 1 Sideways', linewidth=2)
        ax1.plot(df['time'], df['stage1_nadarya'].astype(int), 'g-', label='Stage 1 Nadarya', linewidth=2)
        ax1.plot(df['time'], df['stage2_sideways'].astype(int), 'b--', label='Stage 2 Sideways', linewidth=2)
        ax1.plot(df['time'], df['stage2_nadarya'].astype(int), 'g--', label='Stage 2 Nadarya', linewidth=2)
        ax1.set_ylabel('Condition Met (1=Yes, 0=No)')
        ax1.set_title('Two-Stage Signal Detection Process')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        # Plot 2: Signals Generated
        ax2 = axes[1]
        signal_colors = ['red' if s == -1 else 'green' if s == 1 else 'gray' for s in df['signal']]
        ax2.scatter(df['time'], df['signal'], c=signal_colors, s=50, alpha=0.7)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.axhline(y=1, color='green', linestyle='--', alpha=0.3, label='Call Signal')
        ax2.axhline(y=-1, color='red', linestyle='--', alpha=0.3, label='Put Signal')
        ax2.set_ylabel('Signal (-1=Put, 0=None, 1=Call)')
        ax2.set_title('Generated Trading Signals')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        # Plot 3: Signal Success Rate by Time
        ax3 = axes[2]
        # Calculate rolling success rate (simplified)
        df['has_signal'] = (df['signal'] != 0).astype(int)
        rolling_signals = df['has_signal'].rolling(window=30, min_periods=1).mean()
        ax3.plot(df['time'], rolling_signals, 'purple', linewidth=2, label='Signal Rate (30min rolling)')
        ax3.set_ylabel('Signal Rate')
        ax3.set_title('Signal Generation Rate Over Time')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        # Plot 4: Detailed Signal Breakdown
        ax4 = axes[3]
        # Show when both stages pass
        both_stages = ((df['stage1_sideways'] & df['stage1_nadarya']) & 
                      (df['stage2_sideways'] & df['stage2_nadarya'])).astype(int)
        ax4.fill_between(df['time'], 0, both_stages, alpha=0.3, color='yellow', label='Both Stages Pass')
        ax4.plot(df['time'], df['has_signal'], 'red', linewidth=2, label='Signal Generated')
        ax4.set_ylabel('Condition Met')
        ax4.set_xlabel('Time')
        ax4.set_title('Signal Generation vs Conditions Met')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        
        # Format x-axis for all subplots
        for ax in axes:
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=1))
            ax.xaxis.set_minor_locator(mdates.MinuteLocator(interval=30))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # Save plot
        plot_filename = f'signal_analysis_{self.ticker}_{self.date.replace("-", "")}.png'
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        logger.info(f"📊 Plot saved as {plot_filename}")
        
        plt.show()
        
        return fig
    
    def print_signal_summary(self):
        """Print detailed summary of signal analysis"""
        if not self.minute_data:
            logger.error("❌ No data to summarize. Run analyze_minute_by_minute() first.")
            return
        
        df = pd.DataFrame(self.minute_data)
        
        print("\n" + "="*80)
        print(f"📊 SIGNAL ANALYSIS SUMMARY FOR {self.ticker} on {self.date}")
        print("="*80)
        
        # Basic statistics
        total_minutes = len(df)
        stage1_pass = sum(df['stage1_sideways'] & df['stage1_nadarya'])
        stage2_pass = sum(df['stage2_sideways'] & df['stage2_nadarya'])
        signals_generated = sum(df['signal'] != 0)
        call_signals = sum(df['signal'] == 1)
        put_signals = sum(df['signal'] == -1)
        
        print(f"📈 Total minutes analyzed: {total_minutes}")
        print(f"✅ Stage 1 passed: {stage1_pass} times ({stage1_pass/total_minutes*100:.1f}%)")
        print(f"✅ Stage 2 passed: {stage2_pass} times ({stage2_pass/total_minutes*100:.1f}%)")
        print(f"🎯 Signals generated: {signals_generated} times ({signals_generated/total_minutes*100:.1f}%)")
        print(f"🟢 Call signals: {call_signals}")
        print(f"🔴 Put signals: {put_signals}")
        
        # Show signal times
        signal_times = df[df['signal'] != 0]['time'].tolist()
        if signal_times:
            print(f"\n🕐 Signal times:")
            for i, time in enumerate(signal_times):
                signal_type = "CALL" if df[df['time'] == time]['signal'].iloc[0] == 1 else "PUT"
                signal_text = df[df['time'] == time]['signal_text'].iloc[0]
                print(f"  {i+1}. {time} - {signal_type}: {signal_text}")
        else:
            print("\n❌ No signals generated during the analyzed period")
        
        print("\n" + "="*80)

def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Analyze trading signals minute by minute')
    parser.add_argument('--ticker', default='BATAINDIA', help='Ticker symbol')
    parser.add_argument('--date', default='20-06-2025', help='Date in DD-MM-YYYY format')
    parser.add_argument('--start', default='12:00', help='Start time in HH:MM format')
    parser.add_argument('--end', default='15:30', help='End time in HH:MM format')
    parser.add_argument('--exchange', default='NSE', help='Exchange')
    
    args = parser.parse_args()
    
    logger.info(f"🚀 Starting signal analysis for {args.ticker}")
    
    # Create analyzer
    analyzer = SignalAnalyzer(
        ticker=args.ticker,
        date=args.date,
        start_time=args.start,
        end_time=args.end,
        exchange=args.exchange
    )
    
    # Run analysis
    analyzer.analyze_minute_by_minute()
    
    # Create plots
    analyzer.create_comprehensive_plot()
    
    # Print summary
    analyzer.print_signal_summary()
    
    logger.info("🎉 Signal analysis completed!")

if __name__ == "__main__":
    main()
