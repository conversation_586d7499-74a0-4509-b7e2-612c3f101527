"""
Debug Vectorized Signals

Quick test to debug why the vectorized backtester is not generating signals
at the expected times (12:29, 12:30, 12:31, 12:50, 12:51, 12:54, 12:55).
"""

import sys
import os
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_vectorized_signals():
    """Debug the vectorized backtester signal generation"""
    
    from intelligent_vectorized_backtester import IntelligentVectorizedBacktester
    
    logger.info("🔍 Debugging Enhanced Vectorized Backtester...")
    
    # Test with enhanced features
    backtester = IntelligentVectorizedBacktester(
        ticker='BATAINDIA',
        exchange='NSE',
        start='09:15',
        end='13:00',
        date='24-06-2025',
        tokenid='371',
        enable_momentum_validation=True,
        enable_realtime_detection=True
    )
    
    logger.info("🚀 Running enhanced vectorized backtester...")
    result = backtester.single_vectorized_signal_generation()
    
    logger.info(f"✅ Backtester completed")
    logger.info(f"📊 Success: {result['success']}")
    logger.info(f"📊 Signals found: {len(backtester.signals)}")
    logger.info(f"📊 Positions: {len(backtester.positions)}")
    
    if backtester.signals:
        logger.info("📋 SIGNALS DETECTED:")
        for signal in backtester.signals:
            logger.info(f"   {signal['time']}: {signal['signal_type']} - {signal['reason']}")
    else:
        logger.info("❌ NO SIGNALS DETECTED")
    
    # Expected signals from standalone results:
    expected_signals = [
        {'time': '12:29', 'type': 'CALL', 'description': 'Lower band signal'},
        {'time': '12:30', 'type': 'CALL', 'description': 'Lower band signal'},
        {'time': '12:31', 'type': 'CALL', 'description': 'Lower band signal'},
        {'time': '12:50', 'type': 'PUT', 'description': 'Upper band signal (weak - should be filtered)'},
        {'time': '12:51', 'type': 'PUT', 'description': 'Upper band signal (weak - should be filtered)'},
        {'time': '12:54', 'type': 'PUT', 'description': 'Upper band signal (strong)'},
        {'time': '12:55', 'type': 'PUT', 'description': 'Upper band signal (strong)'},
    ]
    
    logger.info("\n📊 EXPECTED vs ACTUAL COMPARISON:")
    logger.info("Expected signals from standalone results:")
    for expected in expected_signals:
        logger.info(f"   {expected['time']}: {expected['type']} - {expected['description']}")
    
    # Check if any expected signals were found
    found_signals = [signal['time'] for signal in backtester.signals]
    expected_times = [expected['time'] for expected in expected_signals]
    
    matches = set(found_signals) & set(expected_times)
    missing = set(expected_times) - set(found_signals)
    extra = set(found_signals) - set(expected_times)
    
    logger.info(f"\n📊 ANALYSIS:")
    logger.info(f"   Expected: {len(expected_times)} signals")
    logger.info(f"   Found: {len(found_signals)} signals")
    logger.info(f"   Matches: {len(matches)} signals")
    logger.info(f"   Missing: {len(missing)} signals")
    logger.info(f"   Extra: {len(extra)} signals")
    
    if matches:
        logger.info(f"✅ Matched signals: {list(matches)}")
    if missing:
        logger.info(f"❌ Missing signals: {list(missing)}")
    if extra:
        logger.info(f"⚠️ Extra signals: {list(extra)}")
    
    return {
        'expected': len(expected_times),
        'found': len(found_signals),
        'matches': len(matches),
        'missing': list(missing),
        'extra': list(extra),
        'accuracy': len(matches) / len(expected_times) * 100 if expected_times else 0
    }

def main():
    """Main execution function"""
    logger.info("🚀 Starting vectorized signal debugging...")
    
    result = debug_vectorized_signals()
    
    logger.info(f"🎯 FINAL RESULT: {result['accuracy']:.1f}% accuracy")
    
    if result['accuracy'] >= 70:
        logger.info("✅ Good accuracy achieved!")
    else:
        logger.info("❌ Low accuracy - need to investigate further")
    
    logger.info("🎉 Debugging completed!")

if __name__ == "__main__":
    main()
