"""
Comprehensive Test Script for Enhanced Technical Indicators Analyzer

This script tests all the new methods and features:
1. Direct Call Method (ta.indicator_name)
2. Extension Method (df.ta.indicator_name)
3. Extension Kind Method (df.ta(kind="indicator"))
4. Strategy All Method
5. Strategy Common Method
6. Strategy Category Method
7. Custom Strategy Method
8. Category Selection and Exclusion
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from technical_indicators_analyzer import TechnicalIndicatorsAnalyzer

def create_sample_data(num_candles=100):
    """Create sample OHLCV data for testing"""
    np.random.seed(42)
    dates = pd.date_range(start='2025-06-24 09:15', periods=num_candles, freq='1min')
    
    # Generate realistic price data
    base_price = 1000.0
    price_changes = np.random.normal(0, 0.5, num_candles)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] + change
        prices.append(max(new_price, 1))
    
    # Create OHLCV data
    data = []
    for i, date in enumerate(dates):
        open_price = prices[i]
        close_price = prices[i + 1] if i + 1 < len(prices) else prices[i]
        high = max(open_price, close_price) + abs(np.random.normal(0, 0.3))
        low = min(open_price, close_price) - abs(np.random.normal(0, 0.3))
        volume = np.random.randint(1000, 10000)

        data.append({
            'Open': round(open_price, 2),
            'High': round(high, 2),
            'Low': round(low, 2),
            'Close': round(close_price, 2),
            'Volume': volume
        })
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_all_methods():
    """Test all analysis methods"""
    print("🧪 Testing All Analysis Methods")
    print("=" * 60)
    
    # Create sample data
    sample_data = create_sample_data(50)
    print(f"✅ Created sample data with {len(sample_data)} candles")
    
    # Initialize analyzer
    analyzer = TechnicalIndicatorsAnalyzer()
    
    # Test all methods
    methods = [
        'direct_call',
        'extension', 
        'extension_kind',
        'strategy_all',
        'strategy_common',
        'strategy_category',
        'custom_strategy'
    ]
    
    results = {}
    
    for method in methods:
        print(f"\n🔧 Testing {method} method...")
        try:
            start_time = datetime.now()
            
            if method == 'strategy_category':
                # Test with specific categories
                result = analyzer._analyze_dataframe(sample_data, method, ['overlap', 'momentum'])
            else:
                result = analyzer._analyze_dataframe(sample_data, method)
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
                results[method] = {'success': False, 'error': result['error']}
            else:
                indicator_count = len(result.get('indicators', {}))
                print(f"   ✅ Success: {indicator_count} indicators in {processing_time:.3f}s")
                
                # Show sample indicators
                if indicator_count > 0:
                    sample_indicators = list(result['indicators'].keys())[:3]
                    print(f"   📊 Sample: {', '.join(sample_indicators)}")
                
                results[method] = {
                    'success': True,
                    'indicators': indicator_count,
                    'time': processing_time,
                    'categories': result.get('categories_processed', [])
                }
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
            results[method] = {'success': False, 'error': str(e)}
    
    # Summary
    print(f"\n📋 SUMMARY:")
    print(f"{'Method':<20} {'Status':<10} {'Indicators':<12} {'Time (s)':<10}")
    print("-" * 60)
    
    for method, data in results.items():
        if data['success']:
            status = "✅ Success"
            indicators = data['indicators']
            time_taken = f"{data['time']:.3f}"
        else:
            status = "❌ Failed"
            indicators = 0
            time_taken = "N/A"
        
        print(f"{method:<20} {status:<10} {indicators:<12} {time_taken:<10}")

def test_category_selection():
    """Test category selection functionality"""
    print("\n🧪 Testing Category Selection")
    print("=" * 60)
    
    # Create sample data
    sample_data = create_sample_data(30)
    analyzer = TechnicalIndicatorsAnalyzer()
    
    # Test different category combinations
    test_cases = [
        (['overlap'], "Overlap only"),
        (['momentum'], "Momentum only"),
        (['overlap', 'momentum'], "Overlap + Momentum"),
        (['volatility', 'volume'], "Volatility + Volume"),
        (None, "All categories")
    ]
    
    for categories, description in test_cases:
        print(f"\n📂 Testing: {description}")
        try:
            result = analyzer._analyze_dataframe(sample_data, 'extension', categories)
            
            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
            else:
                indicator_count = len(result.get('indicators', {}))
                categories_processed = result.get('categories_processed', [])
                print(f"   ✅ {indicator_count} indicators from {len(categories_processed)} categories")
                print(f"   📂 Categories: {', '.join(categories_processed)}")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")

def test_cli_functionality():
    """Test CLI-like functionality"""
    print("\n🧪 Testing CLI Functionality")
    print("=" * 60)
    
    analyzer = TechnicalIndicatorsAnalyzer()
    
    # Test category listing
    print("📂 Available Categories:")
    for i, category in enumerate(analyzer.categories, 1):
        indicators_count = len(analyzer.category_indicators.get(category, []))
        print(f"  {i}. {category} ({indicators_count} indicators)")
    
    # Test method listing
    print(f"\n🔧 Available Methods:")
    for method, description in analyzer.supported_methods.items():
        print(f"  • {method}: {description}")

def test_performance_comparison():
    """Test performance across different data sizes"""
    print("\n🧪 Testing Performance Comparison")
    print("=" * 60)
    
    analyzer = TechnicalIndicatorsAnalyzer()
    data_sizes = [20, 50, 100]
    methods = ['direct_call', 'extension', 'extension_kind']
    
    print(f"{'Data Size':<12} {'Method':<15} {'Indicators':<12} {'Time (s)':<10}")
    print("-" * 55)
    
    for size in data_sizes:
        sample_data = create_sample_data(size)
        
        for method in methods:
            try:
                start_time = datetime.now()
                result = analyzer._analyze_dataframe(sample_data, method, ['overlap', 'momentum'])
                end_time = datetime.now()
                
                processing_time = (end_time - start_time).total_seconds()
                indicator_count = len(result.get('indicators', {}))
                
                print(f"{size:<12} {method:<15} {indicator_count:<12} {processing_time:<10.3f}")
                
            except Exception as e:
                print(f"{size:<12} {method:<15} {'Error':<12} {'N/A':<10}")

def main():
    """Main test function"""
    print("🚀 Comprehensive Technical Indicators Analyzer Test Suite")
    print("=" * 70)
    
    try:
        # Test 1: All methods
        test_all_methods()
        
        # Test 2: Category selection
        test_category_selection()
        
        # Test 3: CLI functionality
        test_cli_functionality()
        
        # Test 4: Performance comparison
        test_performance_comparison()
        
        print("\n" + "=" * 70)
        print("✅ All tests completed!")
        
        print("\n💡 CLI Usage Examples:")
        print("   # List all categories")
        print("   python technical_indicators_analyzer.py --list-categories")
        
        print("\n   # Use specific categories")
        print("   python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --categories overlap,momentum")
        
        print("\n   # Exclude categories")
        print("   python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --exclude-categories candles,cycles")
        
        print("\n   # Use different methods")
        print("   python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --method direct_call")
        print("   python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --method extension_kind")
        print("   python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --method strategy_category --categories momentum,volatility")
        
    except Exception as e:
        print(f"❌ Test suite failed: {str(e)}")

if __name__ == "__main__":
    main()
