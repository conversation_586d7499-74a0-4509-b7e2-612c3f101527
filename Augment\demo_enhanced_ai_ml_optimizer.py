"""
Demo: Enhanced AI/ML Threshold Optimization System
Works with actual Excel data structure from technical analysis files

🎯 FEATURES DEMONSTRATED:
- Real Excel data loading and transformation
- True signal identification (≥0.5% profit within 15 minutes)
- Multi-algorithm ML optimization
- 14 timeframe combination testing
- Mathematical optimization functions
- Performance validation and reporting
"""

import os
import sys
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_ai_ml_threshold_optimizer import EnhancedAIMLThresholdOptimizer

def demo_enhanced_optimization():
    """Demonstrate the enhanced AI/ML threshold optimization system"""
    
    print("🚀 DEMO: Enhanced AI/ML Threshold Optimization System")
    print("================================================================================")
    print("🎯 OBJECTIVE: Learn from actual Excel data for maximum accuracy")
    print("📊 TRUE SIGNAL: 1min signal → ≥1.0% profit within 15 minutes")
    print("🤖 ML ALGORITHMS: Multi-algorithm ensemble optimization")
    print("🔍 14 TIMEFRAME COMBINATIONS: Complete confirmation learning")
    print("📈 MATHEMATICAL: Advanced optimization functions")
    print("🔧 EXCEL INTEGRATION: Works with actual technical analysis files")
    print("🔄 ITERATIVE TRAINING: Up to 15 iterations until 98% convergence")
    print("📊 COMPREHENSIVE REPORTING: Multi-sheet Excel analysis")
    print("🎯 ALL 8 INDICATORS: Forced detection with AI/ML optimization")
    print("⚡ HIGHER TIMEFRAMES: AI/ML optimized (not just multipliers)")
    print("================================================================================")
    
    # Initialize the enhanced optimizer
    optimizer = EnhancedAIMLThresholdOptimizer()
    
    # Define available data files
    data_files = {
        '1min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_1min_20250714_020337.xlsx',
        '3min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_3min_20250714_020457.xlsx',
        '5min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_5min_20250714_020552.xlsx',
        '15min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_15min_20250714_020711.xlsx',
        '30min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_30min_20250714_020756.xlsx',
        '60min': 'technical_analysis_NATURALGAS26AUG25_MCX_signals_60min_20250714_020823.xlsx'
    }
    
    # Check which files exist
    existing_files = {}
    print("\n🔍 CHECKING AVAILABLE DATA FILES:")
    print("-" * 50)
    
    for timeframe, filepath in data_files.items():
        if os.path.exists(filepath):
            existing_files[timeframe] = filepath
            print(f"✅ {timeframe:>6}: {os.path.basename(filepath)}")
        else:
            print(f"⚠️ {timeframe:>6}: File not found")
    
    if not existing_files:
        print("\n❌ No data files found. Please check file paths.")
        return None
    
    print(f"\n📊 Found {len(existing_files)} timeframe files")
    
    # Test Excel data loading
    print("\n🔧 TESTING EXCEL DATA LOADING:")
    print("-" * 50)
    
    sample_file = list(existing_files.values())[0]
    sample_timeframe = list(existing_files.keys())[0]
    
    print(f"📂 Loading sample file: {os.path.basename(sample_file)}")
    sample_data = optimizer.load_excel_data(sample_file, sample_timeframe)
    
    if sample_data is not None:
        print(f"✅ Successfully loaded and transformed data")
        print(f"   📊 Shape: {sample_data.shape}")
        print(f"   🕒 Time range: {sample_data.index[0]} to {sample_data.index[-1]}")
        print(f"   📈 Columns: {len(sample_data.columns)} indicators")
        
        # Show available indicators
        target_indicators_found = []
        for target in optimizer.target_indicators:
            matching_cols = [col for col in sample_data.columns if target in col]
            if matching_cols:
                target_indicators_found.extend(matching_cols)
        
        if target_indicators_found:
            print(f"   🎯 Target indicators found: {len(target_indicators_found)}")
            for indicator in target_indicators_found[:3]:  # Show first 3
                print(f"      - {indicator}")
            if len(target_indicators_found) > 3:
                print(f"      ... and {len(target_indicators_found) - 3} more")
        else:
            print("   ⚠️ No target indicators found - will use synthetic data")
    else:
        print("❌ Failed to load sample data")
        return None
    
    # Run the comprehensive optimization
    try:
        print("\n🚀 STARTING COMPREHENSIVE OPTIMIZATION...")
        print("=" * 80)
        
        results = optimizer.comprehensive_threshold_optimization(
            existing_files, 
            ticker="NATURALGAS26AUG25_MCX"
        )
        
        # Display results
        display_enhanced_results(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Error during optimization: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def display_enhanced_results(results):
    """Display comprehensive optimization results"""
    
    print("\n🎉 ENHANCED OPTIMIZATION RESULTS SUMMARY")
    print("=" * 80)
    
    if not results or not results.get('validation_successful'):
        print("❌ Optimization was not successful")
        return
    
    # Display iterative training results
    iterative_results = results.get('iterative_results', {})
    if iterative_results:
        print("\n🔄 ITERATIVE TRAINING RESULTS:")
        print("-" * 40)
        print(f"🔄 Iterations Completed:     {iterative_results.get('iterations_completed', 0)}")
        print(f"🎯 Convergence Achieved:     {'YES' if iterative_results.get('convergence_achieved', False) else 'NO'}")

        # Show iteration progress
        all_iterations = iterative_results.get('all_iterations_data', [])
        if all_iterations:
            print(f"📈 Training Progress:")
            for i, iteration_data in enumerate(all_iterations[-3:], max(1, len(all_iterations)-2)):  # Show last 3 iterations
                results_data = iteration_data['results']
                total_true = sum(len(r['true_signals']) for r in results_data.values())
                total_false = sum(len(r['false_signals']) for r in results_data.values())
                total_signals = total_true + total_false
                rate = total_true / max(total_signals, 1) * 100
                print(f"   Iteration {iteration_data['iteration']}: {total_true} true signals ({rate:.1f}%)")

    # Display performance metrics
    metrics = results.get('final_metrics', {})
    print("\n📊 PERFORMANCE METRICS:")
    print("-" * 40)
    print(f"🎯 True Signal Capture Rate: {metrics.get('true_signal_capture_rate', 0):.1f}%")
    print(f"❌ False Signal Rate:        {metrics.get('false_signal_rate', 0):.1f}%")
    print(f"💰 Average Profit/Signal:    {metrics.get('average_profit', 0):.2f}%")
    print(f"📈 Sharpe Ratio:             {metrics.get('sharpe_ratio', 0):.2f}")
    print(f"🏆 Best Combination Score:   {metrics.get('best_combination_score', 0):.3f}")
    
    # Display success criteria
    criteria = results.get('success_criteria', {})
    print("\n🎯 SUCCESS CRITERIA:")
    print("-" * 40)
    print(f"✅ True Signal Capture ≥95%: {'PASS' if criteria.get('true_signal_capture_rate_95') else 'FAIL'}")
    print(f"✅ False Signal Rate ≤30%:   {'PASS' if criteria.get('false_signal_rate_30') else 'FAIL'}")
    print(f"✅ Average Profit ≥0.5%:     {'PASS' if criteria.get('average_profit_05') else 'FAIL'}")
    print(f"✅ Sharpe Ratio ≥2.0:        {'PASS' if criteria.get('sharpe_ratio_2') else 'FAIL'}")
    print(f"🏆 ALL CRITERIA MET:         {'YES' if criteria.get('all_criteria_met') else 'NO'}")
    
    # Display best timeframe combinations
    best_combinations = results.get('best_combinations', [])
    if best_combinations:
        print("\n🏆 BEST TIMEFRAME COMBINATIONS:")
        print("-" * 40)
        for i, combo in enumerate(best_combinations[:5], 1):  # Show top 5
            timeframes = ' + '.join(combo['timeframes'])
            score = combo['score']
            print(f"{i}. {timeframes:<25} (Score: {score:.3f})")
    
    # Display optimized thresholds sample
    optimized_thresholds = results.get('optimized_thresholds', {})
    if optimized_thresholds:
        print("\n🔧 OPTIMIZED THRESHOLDS (Sample):")
        print("-" * 40)
        for indicator in list(optimized_thresholds.keys())[:3]:  # Show first 3 indicators
            print(f"\n{indicator}:")
            thresholds = optimized_thresholds[indicator]
            for key, value in thresholds.items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value:.2f}")
                else:
                    print(f"  {key}: {value}")
    
    # Display Excel report information
    excel_report = results.get('excel_report', '')
    if excel_report:
        print("\n📊 COMPREHENSIVE EXCEL REPORT:")
        print("-" * 40)
        print(f"📄 Excel File: {excel_report}")
        print("📋 Sheets Created:")
        print("   1. Summary - Overall optimization results")
        print("   2. True_Signals - All profitable signals found")
        print("   3. False_Signals - Non-profitable signals")
        print("   4. Iteration_History - Training progress")
        print("   5. Threshold_Evolution - How thresholds changed")
        print("   6. Model_Performance - ML model comparison")
        print("   7. Timeframe_Analysis - Best combinations")
        print("   8. Signal_Timeline - Chronological signal view")
        print("   9. Outlier_Analysis - Special cases detected")
        print("   10. Profitability_Analysis - Profit statistics")

    print("\n" + "=" * 80)
    print("🎉 ENHANCED OPTIMIZATION COMPLETE!")
    print("📄 Detailed JSON report saved")
    print("📊 Comprehensive Excel analysis generated")
    print("🔧 Optimized thresholds ready for trading implementation")
    print("🔄 Iterative training ensures maximum signal capture")

def test_excel_data_structure():
    """Test and display Excel data structure"""
    
    print("\n🔬 TESTING EXCEL DATA STRUCTURE")
    print("=" * 50)
    
    filename = 'technical_analysis_NATURALGAS26AUG25_MCX_signals_1min_20250714_020337.xlsx'
    
    if not os.path.exists(filename):
        print(f"❌ Test file not found: {filename}")
        return
    
    optimizer = EnhancedAIMLThresholdOptimizer()
    
    print(f"📂 Loading: {filename}")
    data = optimizer.load_excel_data(filename, '1min')
    
    if data is not None:
        print(f"✅ Data loaded successfully")
        print(f"   📊 Shape: {data.shape}")
        print(f"   🕒 Time columns: {len(data.index)}")
        print(f"   📈 Indicator columns: {len(data.columns)}")
        
        # Show sample data
        print(f"\n📋 Sample Data (first 5 rows, first 5 columns):")
        print(data.iloc[:5, :5])
        
        # Show available indicators
        print(f"\n🎯 Available Indicators (first 10):")
        for i, col in enumerate(data.columns[:10]):
            print(f"   {i+1:2d}. {col}")
        if len(data.columns) > 10:
            print(f"   ... and {len(data.columns) - 10} more")
    else:
        print("❌ Failed to load data")

def main():
    """Main demo execution"""
    
    print(f"🕒 Enhanced Demo started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test Excel data structure first
    test_excel_data_structure()
    
    # Run the comprehensive demo
    results = demo_enhanced_optimization()
    
    if results:
        print("\n✅ Enhanced demo completed successfully!")
        print("📊 Review the results above for optimization insights")
        print("🔧 The system successfully processed actual Excel data")
    else:
        print("\n⚠️ Enhanced demo completed with issues")
        print("🔧 Check the error messages and adjust as needed")
    
    print(f"\n🕒 Enhanced demo finished: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
