"""
Technical Indicators Analyzer using pandas-ta - REAL API DATA ONLY
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings
import argparse

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import required modules
try:
    import pandas_ta as ta
except ImportError:
    print("❌ pandas-ta not installed. Install with: pip install pandas-ta")
    sys.exit(1)

# Import backtester for REAL data fetching
try:
    from smart_vectorized_backtester import SmartVectorizedBacktester
except ImportError:
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("smart_vectorized_backtester", 
                                                     os.path.join(current_dir, "smart_vectorized_backtester copy.py"))
        smart_vectorized_backtester = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(smart_vectorized_backtester)
        SmartVectorizedBacktester = smart_vectorized_backtester.SmartVectorizedBacktester
    except ImportError:
        print("❌ SmartVectorizedBacktester not found. Cannot fetch real market data.")
        sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TechnicalIndicatorsAnalyzer:
    """REAL API Technical Indicators Analysis System"""

    def __init__(self):
        """Initialize the analyzer with REAL API data support"""
        self.supported_methods = {
            'extension': 'DataFrame extension methods (df.ta.indicator_name)',
            'strategy_all': 'All indicators using AllStrategy'
        }
        logger.info("🚀 REAL API Technical Indicators Analyzer initialized")
    
    def get_market_data(self, ticker: str, exchange: str, date: str, 
                       start_time: str = "09:15", end_time: str = "15:30") -> pd.DataFrame:
        """Get REAL market data for analysis using Shoonya API"""
        try:
            logger.info(f"📡 Fetching REAL market data: {ticker} {exchange} {date}")
            
            backtester = SmartVectorizedBacktester(
                ticker=ticker, exchange=exchange, start=start_time,
                end=end_time, date=date, tokenid=None
            )
            
            data = backtester._fetch_and_process_data()
            
            if data is None or data.empty:
                logger.error(f"❌ No REAL data available for {ticker} on {date}")
                return pd.DataFrame()
            
            # Ensure proper column names for pandas-ta
            if 'volume' in data.columns:
                data = data.rename(columns={'volume': 'Volume'})
            
            logger.info(f"✅ Retrieved {len(data)} REAL candles for {ticker} on {date}")
            return data
            
        except Exception as e:
            logger.error(f"❌ Error getting REAL market data: {str(e)}")
            return pd.DataFrame()
    
    def analyze_signals(self, ticker: str, exchange: str, date: str,
                       method: str = 'extension', categories: List[str] = None) -> Dict:
        """Analyze technical indicators for signals using REAL market data"""
        try:
            logger.info(f"🔍 Analyzing REAL signals for {ticker} on {date}")
            
            market_data = self.get_market_data(ticker, exchange, date)
            if market_data.empty:
                return {'ticker': ticker, 'date': date, 'signals': [], 'analysis': {}}
            
            analysis = self._analyze_dataframe(market_data, method, categories)
            
            return {
                'ticker': ticker, 'date': date, 'exchange': exchange,
                'method': method, 'data_points': len(market_data),
                'analysis': analysis, 'data_source': 'REAL_API_DATA'
            }
            
        except Exception as e:
            logger.error(f"❌ Error analyzing REAL signals: {str(e)}")
            return {'ticker': ticker, 'date': date, 'error': str(e)}
    
    def analyze_full_market_session(self, ticker: str, exchange: str, date: str,
                                  method: str = 'extension', categories: List[str] = None) -> Dict:
        """Analyze technical indicators for complete market session using REAL data"""
        try:
            logger.info(f"🔍 Analyzing REAL full market session for {ticker} on {date}")

            if exchange.upper() == 'MCX':
                start_time, end_time = "09:30", "23:50"
            else:
                start_time, end_time = "09:15", "15:30"

            market_data = self.get_market_data(ticker, exchange, date, start_time, end_time)
            if market_data.empty:
                return {'ticker': ticker, 'date': date, 'session': 'full', 'analysis': {}}

            analysis = self._analyze_dataframe(market_data, method, categories)

            return {
                'ticker': ticker, 'date': date, 'exchange': exchange,
                'method': method, 'session': 'full_market',
                'session_period': f'{start_time}-{end_time}',
                'data_points': len(market_data), 'analysis': analysis,
                'data_source': 'REAL_API_DATA'
            }

        except Exception as e:
            logger.error(f"❌ Error analyzing REAL full session: {str(e)}")
            return {'ticker': ticker, 'date': date, 'session': 'full', 'error': str(e)}
    
    def _analyze_dataframe(self, data: pd.DataFrame, method: str = 'extension', 
                          categories: List[str] = None, functions: List[str] = None) -> Dict:
        """Analyze REAL dataframe with technical indicators"""
        try:
            logger.info(f"📊 Analyzing REAL dataframe with {len(data)} candles using {method}")
            
            if data.empty:
                return {'error': 'No REAL data available for analysis'}
            
            df = data.copy()
            if 'volume' in df.columns:
                df = df.rename(columns={'volume': 'Volume'})
            
            if method == 'extension':
                return self._analyze_extension_method(df, categories, functions)
            elif method == 'strategy_all':
                return self._analyze_strategy_all(df)
            else:
                return self._analyze_extension_method(df, categories, functions)
                
        except Exception as e:
            logger.error(f"❌ Error analyzing REAL dataframe: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_extension_method(self, data: pd.DataFrame, categories: List[str] = None, 
                                functions: List[str] = None) -> Dict:
        """Analyze REAL data using DataFrame extension method"""
        try:
            logger.info("🔧 Using DataFrame Extension Method with REAL data")
            
            df = data.copy()
            
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore")
                
                # Apply indicators to REAL data
                df.ta.rsi(length=14, append=True)
                df.ta.macd(fast=12, slow=26, signal=9, append=True)
                df.ta.cci(length=14, append=True)
                df.ta.smi(fast=5, slow=20, signal=5, append=True)
                df.ta.qqe(length=14, smooth=5, append=True)
                df.ta.cg(length=10, append=True)
                df.ta.pgo(length=14, append=True)
                df.ta.bias(length=26, append=True)
                df.ta.bbands(length=20, std=2, append=True)
                df.ta.atr(length=14, append=True)
                df.ta.accbands(length=10, append=True)
                df.ta.obv(append=True)
                df.ta.mfi(length=14, append=True)
                df.ta.adx(length=14, append=True)
            
            # Extract latest REAL values
            results = {}
            indicator_columns = [col for col in df.columns if col not in ['Open', 'High', 'Low', 'Close', 'Volume', 'time']]
            
            for col in indicator_columns:
                if not df[col].empty and pd.notna(df[col].iloc[-1]):
                    results[col] = float(df[col].iloc[-1])
            
            logger.info(f"✅ Calculated {len(results)} REAL indicators")
            
            return {
                'method': 'extension', 'indicators': results,
                'total_indicators': len(results), 'data_source': 'REAL_MARKET_DATA'
            }
            
        except Exception as e:
            logger.error(f"❌ Error in extension method with REAL data: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_strategy_all(self, data: pd.DataFrame) -> Dict:
        """Analyze REAL data using AllStrategy"""
        try:
            logger.info("🔧 Using AllStrategy with REAL market data")
            
            df = data.copy()
            
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore")
                df.ta.cores = 0
                df.ta.strategy("All")
            
            results = {}
            indicator_columns = [col for col in df.columns if col not in ['Open', 'High', 'Low', 'Close', 'Volume', 'time']]
            
            for col in indicator_columns:
                if not df[col].empty and pd.notna(df[col].iloc[-1]):
                    results[col] = float(df[col].iloc[-1])
            
            logger.info(f"✅ AllStrategy calculated {len(results)} REAL indicators")
            
            return {
                'method': 'strategy_all', 'indicators': results,
                'total_indicators': len(results), 'data_source': 'REAL_MARKET_DATA'
            }
            
        except Exception as e:
            logger.error(f"❌ Error in AllStrategy with REAL data: {str(e)}")
            return {'error': str(e)}

def main():
    """Main function for CLI execution with REAL API data"""
    parser = argparse.ArgumentParser(description='REAL API Technical Indicators Analyzer')
    parser.add_argument('--ticker', required=True, help='Stock ticker symbol')
    parser.add_argument('--exchange', required=True, help='Exchange (MCX/NSE/BSE/NFO)')
    parser.add_argument('--date', required=True, help='Date in DD-MM-YYYY format')
    parser.add_argument('--method', default='extension', help='Analysis method')
    parser.add_argument('--analysis-type', default='full', help='Analysis type')
    
    args = parser.parse_args()
    
    print(f"🔧 REAL API TECHNICAL ANALYZER")
    print(f"📡 FETCHING REAL MARKET DATA (NO SYNTHETIC DATA)")
    print(f"📊 {args.ticker} {args.exchange} {args.date}")
    
    try:
        analyzer = TechnicalIndicatorsAnalyzer()
        
        if args.analysis_type == 'signals':
            result = analyzer.analyze_signals(args.ticker, args.exchange, args.date, args.method)
        else:
            result = analyzer.analyze_full_market_session(args.ticker, args.exchange, args.date, args.method)
        
        if result and 'error' not in result:
            print(f"✅ REAL API analysis completed successfully!")
            print(f"📊 Data points: {result.get('data_points', 0)}")
            print(f"🔧 Method: {result.get('method', 'Unknown')}")
            print(f"📡 Data source: {result.get('data_source', 'Unknown')}")
            
            # Save to Excel
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"technical_analysis_{args.ticker}_{args.exchange}_signals_{timestamp}.xlsx"
            
            analysis = result.get('analysis', {})
            if 'indicators' in analysis:
                indicators_data = []
                for indicator, value in analysis['indicators'].items():
                    indicators_data.append({
                        'Indicator': indicator, 'Value': value, 'Data_Source': 'REAL_API_DATA'
                    })
                
                if indicators_data:
                    df_output = pd.DataFrame(indicators_data)
                    df_output.to_excel(filename, index=False)
                    print(f"📄 REAL data saved to: {filename}")
        else:
            print(f"❌ REAL API analysis failed: {result.get('error', 'Unknown error')}")
            sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error in REAL API analysis: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
