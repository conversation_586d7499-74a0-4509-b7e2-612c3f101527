"""
Integrated Technical Indicators Analyzer with Smart Vectorized Backtester

This system integrates the comprehensive technical indicators analyzer with the 
smart vectorized backtester to provide:

1. Historical backtesting with technical indicators analysis
2. Live market monitoring with real-time indicators
3. Signal-based analysis with comprehensive indicators
4. Excel export for detailed study
5. Support for all exchanges (NSE, BSE, MCX, NFO, CUSTOM)

Features:
- All 4 analysis types (signals, candles, period, full)
- 7 analysis methods (direct_call, extension, etc.)
- 9 indicator categories with 184-243 indicators
- Excel export with detailed analysis
- CLI integration with backtester options
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import argparse
import json

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import required modules
from technical_indicators_analyzer import TechnicalIndicatorsAnalyzer
from shared_api_manager import get_api
from progressive_indicators_calculator import ProgressiveIndicatorsCalculator
from complete_progressive_indicators_calculator import CompleteProgressiveIndicatorsCalculator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('integrated_technical_analyzer.log')
    ]
)
logger = logging.getLogger(__name__)

class IntegratedTechnicalAnalyzer:
    """
    Integrated Technical Analyzer that combines indicators analysis with backtesting
    """
    
    def __init__(self):
        """Initialize the integrated analyzer"""
        self.indicators_analyzer = TechnicalIndicatorsAnalyzer()
        self.progressive_calculator = ProgressiveIndicatorsCalculator()
        self.complete_calculator = CompleteProgressiveIndicatorsCalculator()  # NEW: Complete implementation
        self.api = None
        self.token_cache = {}  # Cache token info to avoid repeated API calls
        logger.info("🚀 Integrated Technical Analyzer initialized with COMPLETE 150+ indicators support")
    
    def get_api_connection(self):
        """Get API connection (always fresh login)"""
        if self.api is None:
            from shared_api_manager import get_api
            self.api = get_api()
        return self.api
    
    def get_token_info(self, ticker: str, exchange: str = 'NSE') -> Optional[Dict]:
        """
        Get token ID and symbol info using searchscrip API
        Supports NSE, BSE, MCX, NFO, CUSTOM exchanges
        """
        # Check cache first
        cache_key = f"{ticker}_{exchange}"
        if cache_key in self.token_cache:
            logger.info(f"📋 Using cached token info for {ticker} on {exchange}")
            return self.token_cache[cache_key]

        try:
            api = self.get_api_connection()
            logger.info(f"🔍 Searching for ticker: {ticker} on {exchange}")

            ret = api.searchscrip(exchange=exchange, searchtext=ticker)

            # Handle empty or invalid response
            if not ret:
                logger.error(f"❌ Empty response from searchscrip API for {ticker} on {exchange}")
                return None

            # Check if response is valid
            if not isinstance(ret, dict):
                logger.error(f"❌ Invalid response format from searchscrip API for {ticker} on {exchange}")
                return None

            # Check API status
            if ret.get('stat') != 'Ok':
                logger.error(f"❌ API error for {ticker} on {exchange}: {ret.get('emsg', 'Unknown error')}")
                return None

            # Check if values exist
            if not ret.get('values'):
                logger.error(f"❌ No results found for ticker: {ticker} on {exchange}")

                # For MCX, suggest common ticker formats
                if exchange == 'MCX':
                    logger.info(f"💡 MCX ticker format suggestions:")
                    logger.info(f"   - For Silver: SILVER, SILVERM, SILVERMINI")
                    logger.info(f"   - For Gold: GOLD, GOLDM, GOLDMINI")
                    logger.info(f"   - For Crude: CRUDEOIL, CRUDEOILM")
                    logger.info(f"   - Try without date suffix: {ticker.split('0')[0] if '0' in ticker else ticker}")

                return None

            values = ret['values']
            logger.info(f"✅ Found {len(values)} results for {ticker} on {exchange}")

            # Log all available options for debugging
            for i, item in enumerate(values[:5]):  # Show first 5 results
                logger.info(f"   {i+1}. {item.get('tsym', 'N/A')} - {item.get('instname', 'N/A')} - {item.get('cname', 'N/A')}")

            # For equity exchanges (NSE, BSE), prefer EQ instruments
            if exchange in ['NSE', 'BSE']:
                for item in values:
                    if item.get('instname') == 'EQ':  # Equity instrument
                        token_info = {
                            'token': item.get('token'),
                            'tsym': item.get('tsym'),
                            'symname': item.get('symname'),
                            'cname': item.get('cname'),
                            'exchange': exchange,
                            'instname': item.get('instname')
                        }
                        logger.info(f"✅ Selected EQ instrument: {token_info['tsym']}")
                        # Cache the result
                        self.token_cache[cache_key] = token_info
                        return token_info

            # For MCX, prefer active contracts (usually the first one)
            if exchange == 'MCX':
                # Sort by expiry date if available, prefer nearest expiry
                active_contracts = []
                for item in values:
                    if item.get('token') and item.get('tsym'):
                        active_contracts.append(item)

                if active_contracts:
                    # Return the first active contract
                    first_match = active_contracts[0]
                    token_info = {
                        'token': first_match.get('token'),
                        'tsym': first_match.get('tsym'),
                        'symname': first_match.get('symname'),
                        'cname': first_match.get('cname'),
                        'exchange': exchange,
                        'instname': first_match.get('instname', 'Unknown')
                    }
                    logger.info(f"✅ Selected MCX contract: {token_info['tsym']}")
                    # Cache the result
                    self.token_cache[cache_key] = token_info
                    return token_info

            # For other exchanges (NFO, etc.) or if no EQ found, return first match
            first_match = values[0]
            token_info = {
                'token': first_match.get('token'),
                'tsym': first_match.get('tsym'),
                'symname': first_match.get('symname'),
                'cname': first_match.get('cname'),
                'exchange': exchange,
                'instname': first_match.get('instname', 'Unknown')
            }
            logger.info(f"✅ Selected first match: {token_info['tsym']}")
            # Cache the result
            self.token_cache[cache_key] = token_info
            return token_info

        except ValueError as e:
            logger.error(f"❌ JSON parsing error for {ticker} on {exchange}: {str(e)}")
            logger.error(f"💡 This usually means the API returned empty or invalid data")
            return None
        except Exception as e:
            logger.error(f"❌ Error searching for ticker {ticker} on {exchange}: {str(e)}")
            return None
    
    def get_market_data(self, ticker: str, exchange: str, date: str,
                       start_time: str = None, end_time: str = None,
                       interval: str = "1") -> Optional[pd.DataFrame]:
        """
        Get market data using get_time_price_series API with configurable intervals

        Args:
            ticker: Stock ticker symbol
            exchange: Exchange name (NSE, BSE, MCX, etc.)
            date: Date in DD-MM-YYYY format
            start_time: Start time in HH:MM format
            end_time: End time in HH:MM format
            interval: Candle interval - "1", "3", "5", "10", "15", "30", "60", "120", "240"
        """
        try:
            # Validate interval parameter
            valid_intervals = ["1", "3", "5", "10", "15", "30", "60", "120", "240"]
            if interval not in valid_intervals:
                logger.warning(f"⚠️ Invalid interval '{interval}', using default '1'")
                interval = "1"

            logger.info(f"📊 Using {interval}-minute candle interval")

            # Set default market hours based on exchange
            if start_time is None or end_time is None:
                if exchange.upper() == 'MCX':
                    # MCX: 10:00 to 23:30 (commodity markets)
                    default_start = "10:00"
                    default_end = "23:30"
                elif exchange.upper() in ['NSE', 'BSE', 'NFO']:
                    # NSE/BSE: 09:15 to 15:30 (equity markets)
                    default_start = "09:15"
                    default_end = "15:30"
                else:
                    # Default fallback
                    default_start = "09:15"
                    default_end = "15:30"

                start_time = start_time or default_start
                end_time = end_time or default_end

                logger.info(f"📅 Using default market hours for {exchange}: {start_time} to {end_time}")

            # Get token info
            token_info = self.get_token_info(ticker, exchange)
            if not token_info:
                logger.error(f"❌ Could not get token info for {ticker} on {exchange}")
                return None

            logger.info(f"📊 Token info: {token_info['tsym']} (Token: {token_info['token']})")

            # Get timestamps using the same method as working backtester
            from enhanced_nadarya_watson_signal import get_start_end_timestamps, live_data
            start_timestamp, end_timestamp = get_start_end_timestamps(date, start_time, end_time)

            # Get data from API using the same approach as working backtester
            api = self.get_api_connection()

            # Add retry logic like the working implementation
            max_retries = 3
            retries = 0
            data = None

            while retries < max_retries:
                try:
                    # Use the same exchange logic as working backtester but with configurable interval
                    if exchange == 'NFO' or exchange == 'NSE':
                        data = api.get_time_price_series(
                            exchange='NSE',
                            token=token_info['token'],
                            starttime=start_timestamp,
                            endtime=end_timestamp,
                            interval=int(interval)  # Convert to integer as required by API
                        )
                    elif exchange == 'MCX':
                        data = api.get_time_price_series(
                            exchange='MCX',
                            token=token_info['token'],
                            starttime=start_timestamp,
                            endtime=end_timestamp,
                            interval=int(interval)  # Convert to integer as required by API
                        )
                    else:
                        # For other exchanges, use the exchange as-is
                        data = api.get_time_price_series(
                            exchange=exchange,
                            token=token_info['token'],
                            starttime=start_timestamp,
                            endtime=end_timestamp,
                            interval=int(interval)  # Convert to integer as required by API
                        )
                    break  # Success, exit retry loop

                except Exception as e:
                    retries += 1
                    if retries == max_retries:
                        logger.error(f"❌ Maximum retries reached for data fetch: {str(e)}")
                        return None
                    else:
                        logger.warning(f"⚠️ Retry {retries}/{max_retries}: {str(e)}")
                        import time
                        time.sleep(1)

            if not data:
                logger.error(f"❌ API returned None for {ticker} on {exchange}")
                logger.error(f"💡 Possible reasons:")
                logger.error(f"   - Date {date} might be a non-trading day (weekend/holiday)")
                logger.error(f"   - Historical data might not be available for this date range")
                logger.error(f"   - API session might need refresh")
                logger.error(f"💡 Try using --force-fresh-login or a more recent date")
                return None

            logger.info(f"📊 API returned data type: {type(data)}, length: {len(data) if hasattr(data, '__len__') else 'N/A'}")
            if data and len(data) > 0:
                logger.info(f"📊 First candle sample: {data[0] if isinstance(data, list) else 'Not a list'}")

            # Process data using the same live_data function as working backtester
            df_processed = live_data(data)

            if df_processed is None or df_processed.empty:
                logger.error(f"❌ No valid data after processing for {ticker} on {exchange}")
                return None

            # FIXED: The live_data function sets 'time' as index, so we need to handle this properly
            # Sort by index (which is time) like the working implementation
            df_processed = df_processed.sort_index()

            # FIXED: Reset index to make 'time' a column for our analyzer
            df_processed = df_processed.reset_index()

            # Ensure proper column naming
            if 'volume' in df_processed.columns:
                df_processed.rename(columns={'volume': 'Volume'}, inplace=True)

            logger.info(f"📊 Pre-fetched {interval}-minute data: {df_processed.shape[0]} candles from {df_processed['time'].iloc[0]} to {df_processed['time'].iloc[-1]}")
            logger.info(f"✅ Retrieved {len(df_processed)} {interval}-minute candles for {ticker} on {exchange}")
            return df_processed

        except Exception as e:
            logger.error(f"❌ Error getting market data: {str(e)}")
            return None
    
    def analyze_with_market_data(self, ticker: str, exchange: str, date: str,
                                mode: str = 'full', method: str = 'extension',
                                categories: List[str] = None, functions: List[str] = None,
                                start_time: str = None, end_time: str = None,
                                candle_times: List[str] = None,
                                include_history: bool = True,
                                interval: str = "1") -> Dict:
        """
        Analyze technical indicators using real market data from API with configurable intervals

        Args:
            interval: Candle interval - "1", "3", "5", "10", "15", "30", "60", "120", "240"
        """
        try:
            # Set default market hours based on exchange if not provided
            if start_time is None or end_time is None:
                if exchange.upper() == 'MCX':
                    # MCX: 09:30 to 23:50 (commodity markets)
                    default_start = "09:30"
                    default_end = "23:50"
                elif exchange.upper() in ['NSE', 'BSE', 'NFO']:
                    # NSE/BSE/NFO: 09:15 to 15:30 (equity markets)
                    default_start = "09:15"
                    default_end = "15:30"
                else:
                    # Default fallback
                    default_start = "09:15"
                    default_end = "15:30"

                start_time = start_time or default_start
                end_time = end_time or default_end

                logger.info(f"📅 Using default market hours for {exchange}: {start_time} to {end_time}")

            logger.info(f"🔍 Starting analysis for {ticker} on {exchange} ({date})")
            logger.info(f"📊 Mode: {mode}, Method: {method}, Categories: {categories}")
            logger.info(f"⏰ Time range: {start_time} to {end_time}, Interval: {interval} minutes")

            # Store interval for progressive calculator
            self._current_interval = interval

            # Get market data with specified interval
            market_data = self.get_market_data(ticker, exchange, date, start_time, end_time, interval)
            if market_data is None:
                return {'error': 'Failed to get market data'}

            # Generate time-series analysis for ALL modes (universal format)
            time_series_result = self._generate_universal_time_series_analysis(
                market_data, method, categories, mode, functions, candle_times, include_history
            )

            # Perform analysis based on mode (for backward compatibility)
            if mode == 'full':
                # FIXED: Use progressive calculation for full mode too
                logger.info(f"🔄 Full mode: Using progressive calculation for all time periods")

                # Generate time periods for full analysis (every 30 minutes)
                full_time_periods = []
                if len(market_data) > 0:
                    # FIXED: Ensure time column exists and is datetime
                    if 'time' not in market_data.columns:
                        logger.error("❌ No 'time' column in market data for full analysis")
                        return {'error': 'No time column in market data'}

                    start_time_dt = pd.to_datetime(market_data['time'].iloc[0])
                    end_time_dt = pd.to_datetime(market_data['time'].iloc[-1])

                    # Generate time periods every 30 minutes
                    current_time = start_time_dt
                    while current_time <= end_time_dt:
                        time_str = current_time.strftime('%H:%M')
                        full_time_periods.append(time_str)
                        current_time += timedelta(minutes=30)

                # FIXED: Generate time-series analysis for the ENTIRE session
                time_series_result = self._generate_universal_time_series_analysis(
                    market_data, method, categories, mode, functions, include_history=False
                )

                if time_series_result and 'time_series_data' in time_series_result:
                    # Get summary from the last time period for backward compatibility
                    time_periods = time_series_result.get('time_periods', [])
                    if time_periods:
                        last_time = time_periods[-1]
                        last_data = time_series_result['time_series_data'].get(last_time, {})

                        result = {
                            'method': method,
                            'indicators': last_data.get('indicators', {}),
                            'total_indicators': len(last_data.get('indicators', {})),
                            'data_points': len(market_data),
                            'time_series_analysis': time_series_result,  # CRITICAL: Include time-series data
                            'full_session_analysis': {  # For backward compatibility
                                'indicators': last_data.get('indicators', {}),
                                'method': method,
                                'categorized_indicators': time_series_result.get('categorized_indicators', {})
                            }
                        }

                        logger.info(f"✅ Full analysis completed with {len(time_periods)} time periods and {len(last_data.get('indicators', {}))} indicators")
                    else:
                        result = {'error': 'No time periods found in time-series analysis'}
                else:
                    result = {'error': 'Failed to generate time-series analysis for full session'}

            elif mode == 'period':
                # FIXED: Generate time-series analysis for the ENTIRE period (not just end time)
                logger.info(f"🔄 Period mode: Generating time-series analysis for ENTIRE period with {method} method")

                if len(market_data) > 0:
                    # FIXED: Ensure time column exists and is datetime
                    if 'time' not in market_data.columns:
                        logger.error("❌ No 'time' column in market data for period analysis")
                        return {'error': 'No time column in market data'}

                    # Generate time-series analysis for the ENTIRE period
                    time_series_result = self._generate_universal_time_series_analysis(
                        market_data, method, categories, mode, functions, include_history=False
                    )

                    if time_series_result and 'time_series_data' in time_series_result:
                        # Get summary from the last time period for backward compatibility
                        time_periods = time_series_result.get('time_periods', [])
                        if time_periods:
                            last_time = time_periods[-1]
                            last_data = time_series_result['time_series_data'].get(last_time, {})

                            result = {
                                'method': method,
                                'indicators': last_data.get('indicators', {}),
                                'total_indicators': len(last_data.get('indicators', {})),
                                'data_points': last_data.get('data_points_used', 0),
                                'time_series_analysis': time_series_result  # CRITICAL: Include time-series data
                            }

                            # DEBUG: Check time-series structure before Excel export
                            print(f"🔍 DEBUG: Time-series analysis keys: {list(time_series_result.keys())}")
                            print(f"🔍 DEBUG: Last data indicators: {len(last_data.get('indicators', {}))}")
                            if 'time_series_data' in time_series_result:
                                ts_data = time_series_result['time_series_data']
                                print(f"🔍 DEBUG: Time-series data has {len(ts_data)} time periods")
                                if ts_data:
                                    # Check a middle time period that should have indicators
                                    time_keys = list(ts_data.keys())
                                    if len(time_keys) > 10:
                                        middle_time = time_keys[len(time_keys)//2]
                                        middle_indicators = ts_data[middle_time].get('indicators', {})
                                        print(f"🔍 DEBUG: Middle time period ({middle_time}) has {len(middle_indicators)} indicators")
                                        sample_indicators = list(middle_indicators.keys())[:10]
                                        print(f"🔍 DEBUG: Sample indicators: {sample_indicators}")

                                        # Check for major indicators
                                        major_found = [ind for ind in ['RSI_14', 'SMA_10', 'EMA_10'] if ind in middle_indicators]
                                        print(f"🔍 DEBUG: Major indicators found: {major_found}")

                            logger.info(f"✅ Period analysis completed with {len(time_periods)} time periods and {len(last_data.get('indicators', {}))} indicators")
                        else:
                            result = {'error': 'No time periods found in time-series analysis'}
                    else:
                        result = {'error': 'Failed to generate time-series analysis for period'}
                else:
                    result = {'error': 'No market data available'}
            elif mode == 'candles':
                if not candle_times:
                    return {'error': 'candle_times required for candles mode'}
                result = self._analyze_specific_candles_with_data(
                    market_data, candle_times, method, categories, include_history
                )
            elif mode == 'signals':
                # For signals mode, we need to integrate with backtester
                result = self._analyze_signals_with_backtester(
                    ticker, exchange, date, market_data, method, categories, functions, include_history, interval
                )
            else:
                return {'error': f'Unknown mode: {mode}'}

            # Merge time-series data with existing result
            if time_series_result:
                result['time_series_analysis'] = time_series_result

            # Add metadata with safe data access
            try:
                data_points = len(market_data) if market_data is not None else 0
                result['metadata'] = {
                    'ticker': ticker,
                    'exchange': exchange,
                    'date': date,
                    'mode': mode,
                    'method': method,
                    'categories': categories,
                    'data_points': data_points,
                    'time_range': f"{start_time} - {end_time}",
                    'interval': f"{interval} minutes",
                    'analysis_timestamp': datetime.now().isoformat()
                }
            except Exception as e:
                logger.warning(f"⚠️ Error creating metadata: {str(e)}")
                result['metadata'] = {
                    'ticker': ticker,
                    'exchange': exchange,
                    'error': 'Error creating metadata'
                }

            return result

        except Exception as e:
            logger.error(f"❌ Error in analysis: {str(e)}")
            return {'error': str(e)}

    def _generate_universal_time_series_analysis(self, market_data: pd.DataFrame,
                                               method: str, categories: List[str], mode: str,
                                               functions: List[str] = None, candle_times: List[str] = None,
                                               include_history: bool = True) -> Dict:
        """
        Generate universal time-series analysis for ALL modes
        Creates data in format: Indicator | Category | Time1 | Time2 | Time3 | ...
        """
        try:
            logger.info(f"🔄 Generating universal time-series analysis for {mode} mode")

            # Prepare market data with time strings - with proper error handling
            try:
                if market_data.index.name == 'time':
                    market_data = market_data.reset_index()
                elif 'time' not in market_data.columns:
                    # Check if index is datetime-like
                    if pd.api.types.is_datetime64_any_dtype(market_data.index):
                        market_data = market_data.reset_index()
                        market_data.rename(columns={'index': 'time'}, inplace=True)
                    else:
                        logger.error("❌ No 'time' column or datetime index found in market data")
                        return {'error': 'No time column or datetime index in market data'}

                # Convert time to datetime if needed
                if not pd.api.types.is_datetime64_any_dtype(market_data['time']):
                    market_data['time'] = pd.to_datetime(market_data['time'])

                market_data['time_str'] = market_data['time'].dt.strftime('%H:%M')

            except Exception as e:
                logger.error(f"❌ Error preparing time data in universal analysis: {str(e)}")
                return {'error': f'Error preparing time data: {str(e)}'}

            # Determine time periods based on mode
            if mode == 'candles' and candle_times:
                # Use specified candle times
                target_times = candle_times
                if include_history:
                    # Add 2 minutes history for each candle time
                    expanded_times = []
                    for candle_time in candle_times:
                        # Find the candle index
                        matching_indices = market_data[market_data['time_str'] == candle_time].index
                        if len(matching_indices) > 0:
                            candle_index = matching_indices[0]
                            # Add 2 minutes before
                            start_idx = max(0, candle_index - 2)
                            for idx in range(start_idx, candle_index + 1):
                                if idx < len(market_data):
                                    time_str = market_data.iloc[idx]['time_str']
                                    if time_str not in expanded_times:
                                        expanded_times.append(time_str)
                    target_times = expanded_times
            elif mode == 'period':
                # Use all available times in the period
                target_times = market_data['time_str'].tolist()
            elif mode == 'full':
                # Use all available times (but sample for performance if too many)
                all_times = market_data['time_str'].tolist()
                if len(all_times) > 50:
                    # Sample every nth time to keep manageable
                    step = len(all_times) // 50
                    target_times = all_times[::step]
                else:
                    target_times = all_times
            elif mode == 'signals':
                # FIXED: For signals mode, use ALL time periods to respect user-specified interval
                # This ensures Excel shows the correct interval spacing
                all_times = market_data['time_str'].tolist()

                # For signals mode, we want to show the actual interval the user requested
                # So we use all available time periods instead of sampling
                target_times = all_times
                logger.info(f"📊 Signals mode: Using all {len(all_times)} time periods to preserve {self._current_interval if hasattr(self, '_current_interval') else '1'}-minute interval spacing")
            else:
                # Default: use all times
                target_times = market_data['time_str'].tolist()

            # Remove duplicates and sort
            target_times = sorted(list(set(target_times)))
            logger.info(f"📊 Analyzing {len(target_times)} time periods: {target_times[:5]}{'...' if len(target_times) > 5 else ''}")

            # CRITICAL FIX: Use progressive calculation instead of full dataset calculation
            logger.info(f"🔄 Using PROGRESSIVE calculation for accurate time-series analysis")

            # CRITICAL FIX: Use user-specified interval, not auto-detected
            interval_minutes = 1
            try:
                # PRIORITY 1: Use user-specified interval if available
                if hasattr(self, '_current_interval') and self._current_interval:
                    interval_minutes = int(self._current_interval)
                    logger.info(f"📊 Using user-specified interval: {interval_minutes} minutes")
                else:
                    # PRIORITY 2: Auto-detect from data frequency only as fallback
                    if len(market_data) > 1:
                        time_diff = (market_data['time'].iloc[1] - market_data['time'].iloc[0]).total_seconds() / 60
                        interval_minutes = int(time_diff)
                        logger.info(f"📊 Auto-detected interval from data: {interval_minutes} minutes")
                    else:
                        logger.info(f"📊 Using default interval: {interval_minutes} minutes")
            except Exception as e:
                logger.warning(f"⚠️ Error determining interval: {e}, using default: 1 minute")
                interval_minutes = 1

            logger.info(f"📊 Using {interval_minutes}-minute interval for progressive calculation")

            # Use COMPLETE progressive calculator for accurate time-series analysis
            progressive_results = self.complete_calculator.calculate_all_indicators_progressive(
                market_data, target_times, interval_minutes, categories, method, functions
            )

            if not progressive_results:
                logger.warning("⚠️ No progressive indicators calculated for time-series analysis")
                return {}

            # Convert progressive results to time-series format
            time_series_data = {}
            all_indicators = {}
            categorized_indicators = {}

            for time_str, result_data in progressive_results.items():
                indicators = result_data['indicators']
                price_data = result_data['price_data']

                time_series_data[time_str] = {
                    'indicators': indicators,
                    'price_data': price_data
                }

                # Collect all unique indicators for categorization
                for indicator_name, value in indicators.items():
                    all_indicators[indicator_name] = value

            logger.info(f"✅ Progressive calculation completed: {len(all_indicators)} unique indicators across {len(time_series_data)} time periods")

            # Create categorized indicators for compatibility
            for category in categories or ['momentum', 'overlap', 'volatility', 'volume']:
                categorized_indicators[category] = {}
                for indicator_name, value in all_indicators.items():
                    if category.lower() in indicator_name.lower():
                        categorized_indicators[category][indicator_name] = value

            return {
                'method': f'time_series_{method}',
                'mode': mode,
                'time_periods': target_times,
                'time_series_data': time_series_data,
                'categorized_indicators': categorized_indicators,
                'total_indicators': len(all_indicators),
                'total_time_periods': len(target_times),
                'note': 'Universal time-series format: Indicator | Category | Time1 | Time2 | ...'
            }

        except Exception as e:
            logger.error(f"❌ Error generating time-series analysis: {str(e)}")
            return {}

    def _analyze_specific_candles_with_data(self, market_data: pd.DataFrame,
                                          candle_times: List[str], method: str,
                                          categories: List[str], include_history: bool) -> Dict:
        """Analyze specific candles using market data"""
        results = {}
        all_time_periods = []

        # Convert market data time index to string format for matching
        # The live_data function sets time as index, so we need to work with the index
        if market_data.index.name != 'time' and 'time' not in market_data.columns:
            logger.error("❌ No 'time' index or column found in market data")
            return {'error': 'No time index or column in market data'}

        # Reset index to make time a column if it's currently the index
        if market_data.index.name == 'time':
            market_data = market_data.reset_index()

        # Convert time to datetime if it's not already
        if not pd.api.types.is_datetime64_any_dtype(market_data['time']):
            try:
                market_data['time'] = pd.to_datetime(market_data['time'])
            except Exception as e:
                logger.error(f"❌ Error converting time column: {str(e)}")
                return {'error': f'Error converting time column: {str(e)}'}

        market_data['time_str'] = market_data['time'].dt.strftime('%H:%M')

        for candle_time in candle_times:
            try:
                logger.info(f"🕐 Analyzing candle time: {candle_time}")

                # Find the exact candle index for the specified time
                matching_indices = market_data[market_data['time_str'] == candle_time].index

                if len(matching_indices) == 0:
                    logger.warning(f"⚠️ No data found for time {candle_time}")
                    # Find closest time
                    market_data['time_diff'] = abs(pd.to_datetime(market_data['time_str'], format='%H:%M') -
                                                 pd.to_datetime(candle_time, format='%H:%M'))
                    closest_idx = market_data['time_diff'].idxmin()
                    candle_index = closest_idx
                    logger.info(f"📍 Using closest time: {market_data.loc[closest_idx, 'time_str']}")
                else:
                    candle_index = matching_indices[0]
                    logger.info(f"✅ Found exact match for {candle_time}")

                # Collect time periods for this candle
                time_periods = []

                if include_history and candle_index >= 2:
                    # Include 2 minutes of history: [candle_time-2, candle_time-1, candle_time]
                    start_idx = max(0, candle_index - 2)
                    end_idx = candle_index + 1
                    display_data = market_data.iloc[start_idx:end_idx].copy()

                    for idx in range(len(display_data)):
                        period_time = display_data.iloc[idx]['time_str']
                        time_periods.append(period_time)
                        if period_time not in all_time_periods:
                            all_time_periods.append(period_time)
                else:
                    # Just the specific candle
                    display_data = market_data.iloc[candle_index:candle_index+1].copy()
                    period_time = display_data.iloc[0]['time_str']
                    time_periods.append(period_time)
                    if period_time not in all_time_periods:
                        all_time_periods.append(period_time)

                # For indicator calculation, use more historical data (minimum 50 candles)
                # This ensures technical indicators have enough data to calculate meaningful values
                indicator_start_idx = max(0, candle_index - 49)  # 50 candles total
                indicator_end_idx = candle_index + 1
                indicator_data = market_data.iloc[indicator_start_idx:indicator_end_idx].copy()

                logger.info(f"📊 Using {len(indicator_data)} candles for indicator calculation (from {indicator_data.iloc[0]['time_str']} to {indicator_data.iloc[-1]['time_str']})")

                logger.info(f"📊 Analyzing {len(time_periods)} time periods for {candle_time}: {time_periods}")

                # Calculate indicators using ALL available market data for accuracy
                # Exclude time columns from indicators calculation
                full_indicator_data = market_data.drop(columns=['time', 'time_str'], errors='ignore')

                logger.info(f"📊 Using {len(full_indicator_data)} candles for accurate indicator calculation")

                try:
                    # Calculate indicators using full dataset
                    full_analysis = self.indicators_analyzer._analyze_dataframe(full_indicator_data, method, categories, functions)

                    if full_analysis and 'indicators' in full_analysis:
                        logger.info(f"✅ Successfully calculated {len(full_analysis['indicators'])} indicators using full dataset")

                        # Now extract results for each requested time period
                        period_results = {}
                        for period_time in time_periods:
                            # Find the row for this time period
                            period_rows = market_data[market_data['time_str'] == period_time]
                            if len(period_rows) > 0:
                                row = period_rows.iloc[0]

                                # Get price data for this period
                                volume_col = 'volume' if 'volume' in market_data.columns else 'Volume'
                                price_data = {
                                    'Open': row['Open'],
                                    'High': row['High'],
                                    'Low': row['Low'],
                                    'Close': row['Close'],
                                    'Volume': row[volume_col]
                                }

                                # Use the indicators calculated from full dataset
                                period_results[period_time] = {
                                    'indicators': full_analysis['indicators'].copy(),
                                    'price_data': price_data
                                }
                                logger.debug(f"✅ Extracted {len(full_analysis['indicators'])} indicators for {period_time}")
                            else:
                                logger.warning(f"⚠️ No data found for time period {period_time}")
                    else:
                        logger.warning(f"⚠️ No indicators calculated from full dataset")
                        period_results = {}

                except Exception as e:
                    logger.error(f"❌ Error calculating indicators: {str(e)}")
                    period_results = {}

                results[candle_time] = {
                    'periods': period_results,
                    'time_periods': time_periods,
                    'requested_time': candle_time
                }

            except Exception as e:
                logger.warning(f"⚠️ Error analyzing candle {candle_time}: {str(e)}")
                results[candle_time] = {'error': str(e)}

        return {
            'method': 'candles_analysis',
            'candles': results,
            'total_candles': len(candle_times),
            'all_time_periods': sorted(list(set(all_time_periods)))
        }
    
    def _analyze_signals_with_backtester(self, ticker: str, exchange: str, date: str,
                                       market_data: pd.DataFrame, method: str,
                                       categories: List[str], functions: List[str], include_history: bool, interval: str = "1") -> Dict:
        """Analyze signals using integrated backtester with proper interval handling"""
        try:
            logger.info(f"🔍 Signals analysis using {interval}-minute interval data")

            # Import backtester
            sys.path.append(current_dir)
            smart_backtester = __import__('smart_vectorized_backtester copy')
            SmartVectorizedBacktester = smart_backtester.SmartVectorizedBacktester

            # Get token info
            token_info = self.get_token_info(ticker, exchange)
            if not token_info:
                return {'error': 'Could not get token info for backtester'}

            # Run backtester to get actual signals using the market_data with correct interval
            backtester = SmartVectorizedBacktester(
                ticker=ticker,
                exchange=exchange,
                start="09:15",
                end="15:30",
                date=date,
                tokenid=token_info['token']
            )

            # Get actual signals from backtester
            signal_times = []
            try:
                signals = backtester.run_smart_vectorized_backtest()
                if signals and len(signals) > 0:
                    signal_times = [signal['time'] for signal in signals]
                    logger.info(f"📊 Found {len(signal_times)} actual signals from backtester")
                else:
                    logger.info("📊 No signals found from backtester, using fallback analysis")
            except Exception as e:
                logger.warning(f"⚠️ Could not get signals from backtester: {e}")

            # ALWAYS use fallback if no signals from backtester
            if not signal_times:
                logger.info("📊 Using fallback time periods for analysis")
                # Extract key time periods from market data for analysis
                if len(market_data) > 0:
                    # Use every 30th candle as signal times for analysis
                    step = max(1, len(market_data) // 10)  # Get ~10 analysis points
                    for i in range(0, len(market_data), step):
                        time_str = market_data.iloc[i]['time'].strftime('%H:%M')
                        signal_times.append(time_str)
                    logger.info(f"📊 Generated {len(signal_times)} fallback time periods: {signal_times[:5]}...")
                else:
                    # Last resort: use fixed time periods
                    signal_times = ["09:30", "10:30", "11:30", "12:30", "13:30", "14:30", "15:00"]
                    logger.info(f"📊 Using fixed time periods: {signal_times}")

            if not signal_times:
                return {'error': 'No signal times available for analysis'}
            
            # Analyze indicators at signal times using the correct interval data
            signal_results = {}
            logger.info(f"📊 Analyzing indicators for {len(signal_times)} signal times using {interval}-minute data")

            for signal_time in signal_times:
                try:
                    # Find the exact time match in market data
                    signal_index = None
                    for i, row in market_data.iterrows():
                        if row['time'].strftime('%H:%M') == signal_time:
                            signal_index = market_data.index.get_loc(i)
                            break

                    if signal_index is None:
                        # If exact match not found, find closest time
                        time_diffs = []
                        for i, row in market_data.iterrows():
                            time_str = row['time'].strftime('%H:%M')
                            try:
                                signal_dt = datetime.strptime(signal_time, '%H:%M').time()
                                data_dt = datetime.strptime(time_str, '%H:%M').time()
                                diff = abs((datetime.combine(datetime.today(), signal_dt) -
                                          datetime.combine(datetime.today(), data_dt)).total_seconds())
                                time_diffs.append((i, diff))
                            except:
                                continue

                        if time_diffs:
                            closest_idx, _ = min(time_diffs, key=lambda x: x[1])
                            signal_index = market_data.index.get_loc(closest_idx)

                    if signal_index is not None:
                        if include_history and signal_index >= 2:
                            data_slice = market_data.iloc[max(0, signal_index-2):signal_index+1]
                        else:
                            data_slice = market_data.iloc[signal_index:signal_index+1]

                        analysis = self.indicators_analyzer._analyze_dataframe(data_slice, method, categories, functions)
                        signal_results[signal_time] = analysis
                        logger.debug(f"✅ Analyzed signal at {signal_time} using {len(data_slice)} candles")
                    else:
                        logger.warning(f"⚠️ Could not find data for signal time {signal_time}")

                except Exception as e:
                    logger.warning(f"⚠️ Error analyzing signal at {signal_time}: {e}")
                    continue
            
            logger.info(f"✅ Signals analysis completed using {interval}-minute interval data")
            logger.info(f"📊 Analyzed {len(signal_results)} signals out of {len(signal_times)} signal times")

            return {
                'method': 'signals_analysis',
                'signals': signal_results,
                'total_signals': len(signal_times),
                'interval_used': f"{interval} minutes",
                'data_points': len(market_data)
            }
            
        except Exception as e:
            logger.error(f"❌ Error in signals analysis: {str(e)}")
            return {'error': str(e)}

    def export_to_excel(self, results: Dict, filename: str = None) -> str:
        """
        Export analysis results to Excel format for detailed study
        """
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                metadata = results.get('metadata', {})
                ticker = metadata.get('ticker', 'UNKNOWN')
                exchange = metadata.get('exchange', 'UNKNOWN')
                mode = metadata.get('mode', 'analysis')
                filename = f"technical_analysis_{ticker}_{exchange}_{mode}_{timestamp}.xlsx"

            logger.info(f"📊 Exporting results to {filename}")

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                sheets_created = 0

                # Summary sheet (always create this)
                try:
                    self._create_summary_sheet(results, writer)
                    sheets_created += 1
                    logger.debug("✅ Summary sheet created")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to create summary sheet: {str(e)}")

                # Universal Time-Series Indicators sheet (NEW FORMAT)
                try:
                    print(f"🔍 DEBUG: Attempting to create universal time-series sheet...")
                    print(f"🔍 DEBUG: Results keys: {list(results.keys())}")
                    print(f"🔍 DEBUG: Has time_series_analysis: {'time_series_analysis' in results}")

                    timeseries_created = self._create_universal_timeseries_sheet(results, writer)
                    print(f"🔍 DEBUG: Time-series sheet created: {timeseries_created}")
                    if timeseries_created:
                        sheets_created += 1
                        logger.debug("✅ Universal time-series indicators sheet created")
                    else:
                        logger.debug("ℹ️ No time-series data available, creating fallback indicators sheet")
                        # Fallback to original indicators sheet
                        indicators_created = self._create_indicators_sheet(results, writer)
                        if indicators_created:
                            sheets_created += 1
                            logger.debug("✅ Fallback indicators sheet created")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to create time-series indicators sheet: {str(e)}")
                    # Try fallback
                    try:
                        indicators_created = self._create_indicators_sheet(results, writer)
                        if indicators_created:
                            sheets_created += 1
                            logger.debug("✅ Fallback indicators sheet created")
                    except Exception as fallback_e:
                        logger.warning(f"⚠️ Fallback indicators sheet also failed: {str(fallback_e)}")

                # Metadata sheet
                try:
                    self._create_metadata_sheet(results, writer)
                    sheets_created += 1
                    logger.debug("✅ Metadata sheet created")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to create metadata sheet: {str(e)}")

                # If signals analysis, create signals sheet
                if 'signal_analyses' in results:
                    try:
                        signals_created = self._create_signals_sheet(results, writer)
                        if signals_created:
                            sheets_created += 1
                            logger.debug("✅ Signals sheet created")
                        else:
                            logger.debug("ℹ️ No signals sheet needed")
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to create signals sheet: {str(e)}")

                # If candles analysis, create candles sheet
                if 'candles' in results:
                    try:
                        candles_created = self._create_candles_sheet(results, writer)
                        if candles_created:
                            sheets_created += 1
                            logger.debug("✅ Candles sheet created")
                        else:
                            logger.debug("ℹ️ No candles sheet needed")
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to create candles sheet: {str(e)}")

                # Ensure at least one sheet was created
                if sheets_created == 0:
                    logger.warning("⚠️ No sheets were created, creating basic summary")
                    # Create a basic summary sheet as fallback
                    basic_summary = pd.DataFrame([
                        ['Analysis', 'Technical Indicators Analysis'],
                        ['Status', 'Completed'],
                        ['Timestamp', datetime.now().isoformat()]
                    ], columns=['Property', 'Value'])
                    basic_summary.to_excel(writer, sheet_name='Summary', index=False)
                    sheets_created += 1

                logger.info(f"📊 Created {sheets_created} Excel sheets")

            logger.info(f"✅ Excel export completed: {filename}")
            return filename

        except Exception as e:
            logger.error(f"❌ Error exporting to Excel: {str(e)}")
            return None

    def _create_summary_sheet(self, results: Dict, writer):
        """Create summary sheet in Excel"""
        summary_data = []

        # Basic info
        metadata = results.get('metadata', {})
        summary_data.append(['Analysis Type', metadata.get('mode', 'Unknown')])
        summary_data.append(['Ticker', metadata.get('ticker', 'Unknown')])
        summary_data.append(['Exchange', metadata.get('exchange', 'Unknown')])
        summary_data.append(['Date', metadata.get('date', 'Unknown')])
        summary_data.append(['Method', metadata.get('method', 'Unknown')])
        categories_list = metadata.get('categories', [])
        if isinstance(categories_list, list):
            categories_str = ', '.join(categories_list)
        else:
            categories_str = str(categories_list)
        summary_data.append(['Categories', categories_str])
        summary_data.append(['Data Points', metadata.get('data_points', 0)])
        summary_data.append(['Time Range', metadata.get('time_range', 'Unknown')])
        summary_data.append(['Analysis Timestamp', metadata.get('analysis_timestamp', 'Unknown')])

        # Results summary
        if 'indicators' in results:
            summary_data.append(['Total Indicators', len(results['indicators'])])
        if 'total_signals' in results:
            summary_data.append(['Total Signals', results['total_signals']])
        if 'total_candles' in results:
            summary_data.append(['Total Candles', results['total_candles']])

        # Enhanced summary for automated all indicators method
        if 'full_session_analysis' in results:
            analysis = results['full_session_analysis']
            if analysis.get('method') == 'strategy_all_automated':
                summary_data.append(['--- AUTOMATED ALL INDICATORS ---', '---'])
                summary_data.append(['Total Columns Generated', analysis.get('total_columns_generated', 0)])
                summary_data.append(['Processed Indicators', analysis.get('processed_indicators', 0)])

                performance_settings = analysis.get('performance_settings', {})
                summary_data.append(['Cores Used', performance_settings.get('cores', 'Unknown')])
                summary_data.append(['Timed Analysis', performance_settings.get('timed', 'Unknown')])

                calc_time = performance_settings.get('calculation_time_seconds')
                if calc_time:
                    summary_data.append(['Calculation Time (seconds)', f"{calc_time:.2f}"])

                categorized = analysis.get('categorized_indicators', {})
                if categorized:
                    summary_data.append(['--- INDICATORS BY CATEGORY ---', '---'])
                    for category, indicators in categorized.items():
                        summary_data.append([f'{category.title()} Indicators', len(indicators)])

        summary_df = pd.DataFrame(summary_data, columns=['Property', 'Value'])
        summary_df.to_excel(writer, sheet_name='Summary', index=False)

    def _create_indicators_sheet(self, results: Dict, writer) -> bool:
        """Create indicators sheet in Excel with enhanced support for automated all indicators"""
        indicators_data = []

        # Check for indicators in different possible locations
        if 'full_session_analysis' in results and 'indicators' in results['full_session_analysis']:
            indicators = results['full_session_analysis']['indicators']
            categorized_indicators = results['full_session_analysis'].get('categorized_indicators', {})
            method = results['full_session_analysis'].get('method', 'unknown')
        elif 'indicators' in results:
            indicators = results['indicators']
            categorized_indicators = results.get('categorized_indicators', {})
            method = results.get('method', 'unknown')
        else:
            indicators = {}
            categorized_indicators = {}
            method = 'unknown'

        if indicators:
            # If we have categorized indicators (from automated method), use those categories
            if categorized_indicators and method == 'strategy_all_automated':
                for category, indicator_list in categorized_indicators.items():
                    for indicator in indicator_list:
                        if indicator in indicators:
                            indicators_data.append({
                                'Indicator': indicator,
                                'Category': category,
                                'Value': indicators[indicator],
                                'Method': method
                            })

                # Add any indicators not in categories
                categorized_indicator_names = set()
                for indicator_list in categorized_indicators.values():
                    categorized_indicator_names.update(indicator_list)

                for indicator, value in indicators.items():
                    if indicator not in categorized_indicator_names:
                        indicators_data.append({
                            'Indicator': indicator,
                            'Category': 'other',
                            'Value': value,
                            'Method': method
                        })
            else:
                # Fallback to original parsing method
                for indicator, value in indicators.items():
                    # Parse indicator name to extract category and parameters
                    parts = indicator.split('_')
                    category = parts[0] if parts else 'unknown'

                    indicators_data.append({
                        'Indicator': indicator,
                        'Category': category,
                        'Value': value,
                        'Method': method
                    })

            if indicators_data:
                indicators_df = pd.DataFrame(indicators_data)

                # Sort by category and then by indicator name for better organization
                indicators_df = indicators_df.sort_values(['Category', 'Indicator'])

                indicators_df.to_excel(writer, sheet_name='Indicators', index=False)

                # If we have many indicators (automated method), also create a summary by category
                if len(indicators_data) > 50:
                    self._create_indicators_summary_sheet(categorized_indicators, indicators, writer)

                return True

        return False

    def _create_indicators_summary_sheet(self, categorized_indicators: Dict, indicators: Dict, writer):
        """Create a summary sheet for indicators by category (for automated all indicators)"""
        try:
            summary_data = []

            for category, indicator_list in categorized_indicators.items():
                category_count = len(indicator_list)
                # Calculate some basic statistics for the category
                category_values = [indicators[ind] for ind in indicator_list if ind in indicators]

                if category_values:
                    avg_value = sum(category_values) / len(category_values)
                    min_value = min(category_values)
                    max_value = max(category_values)
                else:
                    avg_value = min_value = max_value = 0

                summary_data.append({
                    'Category': category,
                    'Indicator_Count': category_count,
                    'Average_Value': round(avg_value, 4),
                    'Min_Value': round(min_value, 4),
                    'Max_Value': round(max_value, 4)
                })

            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Indicators_Summary', index=False)
                logger.info(f"✅ Created indicators summary sheet with {len(summary_data)} categories")

        except Exception as e:
            logger.warning(f"⚠️ Failed to create indicators summary sheet: {str(e)}")

    def _create_universal_timeseries_sheet(self, results: Dict, writer) -> bool:
        """
        Create universal time-series sheet in format: Indicator | Category | Time1 | Time2 | Time3 | ...
        Works for ALL analysis types and methods
        """
        try:
            # Check for time-series data
            time_series_analysis = results.get('time_series_analysis', {})
            if not time_series_analysis:
                logger.debug("ℹ️ No time-series analysis data found")
                return False

            time_series_data = time_series_analysis.get('time_series_data', {})
            time_periods = time_series_analysis.get('time_periods', [])
            categorized_indicators = time_series_analysis.get('categorized_indicators', {})

            if not time_series_data or not time_periods:
                logger.debug("ℹ️ No time-series data or time periods found")
                return False

            logger.info(f"📊 Creating universal time-series sheet with {len(time_periods)} time periods")

            # FIXED: Find the time period with the MOST indicators (not just the first)
            all_indicators = {}
            template_time = None
            max_indicators = 0

            print(f"🔍 DEBUG: Searching for best template among {len(time_periods)} time periods...")

            for time_period in time_periods:
                if time_period in time_series_data:
                    indicators = time_series_data[time_period].get('indicators', {})
                    indicator_count = len(indicators)
                    print(f"   Time {time_period}: {indicator_count} indicators")

                    if indicator_count > max_indicators:
                        all_indicators = indicators
                        template_time = time_period
                        max_indicators = indicator_count
                        print(f"   ✅ New best template: {time_period} with {indicator_count} indicators")

            if not all_indicators:
                logger.warning("⚠️ No indicators found in any time period")
                return False

            print(f"📊 Using time period {template_time} as template with {len(all_indicators)} indicators")

            # DEBUG: Show sample indicators being processed
            sample_indicators = list(all_indicators.keys())[:10]
            print(f"🔍 Sample indicators: {sample_indicators}")

            # DEBUG: Check if major indicators are present
            major_indicators = ['RSI_14', 'SMA_10', 'EMA_10', 'MACD_12_26_9_MACD_12_26_9', 'ATR_14']
            found_major = [ind for ind in major_indicators if ind in all_indicators]
            missing_major = [ind for ind in major_indicators if ind not in all_indicators]
            print(f"✅ Found major indicators: {found_major}")
            print(f"❌ Missing major indicators: {missing_major}")

            # Create the data structure: indicators as rows, time periods as columns
            sheet_data = []

            # First, add price data rows
            price_metrics = ['Open', 'High', 'Low', 'Close', 'Volume']
            for metric in price_metrics:
                row = {'Indicator': metric, 'Category': 'Price'}
                for time_period in time_periods:
                    if time_period in time_series_data:
                        price_data = time_series_data[time_period].get('price_data', {})
                        # Handle both 'Volume' and 'volume' keys
                        if metric == 'Volume':
                            value = price_data.get('Volume', price_data.get('volume', ''))
                        else:
                            value = price_data.get(metric, '')
                        row[time_period] = value
                    else:
                        row[time_period] = ''
                sheet_data.append(row)

            # Add separator row
            separator_row = {'Indicator': '--- TECHNICAL INDICATORS ---', 'Category': '---'}
            for time_period in time_periods:
                separator_row[time_period] = '---'
            sheet_data.append(separator_row)

            # FIXED: Always use ALL indicators (not just categorized ones)
            # Add ALL indicator rows - use proper categorization based on pandas-ta categories
            logger.info(f"📊 Adding {len(all_indicators)} indicators to time-series sheet")

            for indicator in sorted(all_indicators.keys()):
                # FIXED: Determine category based on pandas-ta indicator categories
                category = 'technical'  # Default category

                # Parse indicator name to extract category
                indicator_lower = indicator.lower()
                if any(vol_term in indicator_lower for vol_term in ['volume', 'vp_', 'vwap', 'ad', 'obv', 'cmf', 'mfi']):
                    category = 'volume'
                elif any(mom_term in indicator_lower for mom_term in ['rsi', 'macd', 'stoch', 'cci', 'roc', 'mom', 'apo', 'ppo', 'trix']):
                    category = 'momentum'
                elif any(vol_term in indicator_lower for vol_term in ['atr', 'bb', 'kc', 'dc', 'ui', 'natr', 'true_range']):
                    category = 'volatility'
                elif any(trend_term in indicator_lower for trend_term in ['sma', 'ema', 'wma', 'dema', 'tema', 'trima', 'kama', 'mama', 'fama']):
                    category = 'overlap'
                elif any(cycle_term in indicator_lower for cycle_term in ['ht_', 'mesa']):
                    category = 'cycle'
                elif any(stat_term in indicator_lower for stat_term in ['correl', 'beta', 'tsf', 'var']):
                    category = 'statistics'

                row = {'Indicator': indicator, 'Category': category}

                # Collect values for this indicator across all time periods
                for time_period in time_periods:
                    if time_period in time_series_data:
                        indicators_data = time_series_data[time_period].get('indicators', {})
                        value = indicators_data.get(indicator, '')
                    else:
                        value = ''
                    row[time_period] = value

                sheet_data.append(row)

            # Create DataFrame and export
            if sheet_data:
                timeseries_df = pd.DataFrame(sheet_data)

                # Reorder columns: Indicator, Category, then time periods in chronological order
                column_order = ['Indicator', 'Category'] + time_periods
                timeseries_df = timeseries_df[column_order]

                # Export to Excel
                sheet_name = 'Time_Series_Indicators'
                timeseries_df.to_excel(writer, sheet_name=sheet_name, index=False)

                logger.info(f"✅ Universal time-series sheet created with {len(sheet_data)} indicators and {len(time_periods)} time periods")
                logger.info(f"📊 Format: Indicator | Category | {' | '.join(time_periods[:3])}{'...' if len(time_periods) > 3 else ''}")
                return True

            return False

        except Exception as e:
            logger.error(f"❌ Error creating universal time-series sheet: {str(e)}")
            return False

    def _create_metadata_sheet(self, results: Dict, writer):
        """Create metadata sheet in Excel"""
        metadata = results.get('metadata', {})
        metadata_data = []

        for key, value in metadata.items():
            if isinstance(value, (list, dict)):
                value = str(value)
            metadata_data.append([key, value])

        metadata_df = pd.DataFrame(metadata_data, columns=['Property', 'Value'])
        metadata_df.to_excel(writer, sheet_name='Metadata', index=False)

    def _create_signals_sheet(self, results: Dict, writer) -> bool:
        """Create signals analysis sheet in Excel"""
        # Check both 'signals' and 'signal_analyses' keys
        signals_data = []

        if 'signal_analyses' in results:
            for signal_time, signal_data in results['signal_analyses'].items():
                if isinstance(signal_data, dict) and 'indicators' in signal_data:
                    for indicator, value in signal_data['indicators'].items():
                        signals_data.append({
                            'Signal_Time': signal_time,
                            'Indicator': indicator,
                            'Value': value
                        })
        elif 'signals' in results:
            for signal_time, signal_data in results['signals'].items():
                if isinstance(signal_data, dict) and 'indicators' in signal_data:
                    for indicator, value in signal_data['indicators'].items():
                        signals_data.append({
                            'Signal_Time': signal_time,
                            'Indicator': indicator,
                            'Value': value
                        })

        if signals_data:
            signals_df = pd.DataFrame(signals_data)
            signals_df.to_excel(writer, sheet_name='Signals_Analysis', index=False)
            return True

        return False

    def _create_candles_sheet(self, results: Dict, writer) -> bool:
        """Create candles analysis sheet in Excel with proper time-series format"""
        if 'candles' not in results:
            return False

        # Get all time periods from the results
        all_time_periods = results.get('all_time_periods', [])
        if not all_time_periods:
            # Fallback: collect time periods from candle data
            all_time_periods = set()
            for _, candle_data in results['candles'].items():
                if isinstance(candle_data, dict) and 'periods' in candle_data:
                    all_time_periods.update(candle_data['periods'].keys())
                elif isinstance(candle_data, dict) and 'time_periods' in candle_data:
                    all_time_periods.update(candle_data['time_periods'])
            all_time_periods = sorted(list(all_time_periods))

        if not all_time_periods:
            logger.warning("⚠️ No time periods found for candles analysis")
            return False

        logger.info(f"📊 Creating candles sheet with time periods: {all_time_periods}")

        # Collect all indicators and their categories
        all_indicators = {}  # indicator_name -> category
        price_data_collected = {}  # time_period -> price_data

        for candle_time, candle_data in results['candles'].items():
            if isinstance(candle_data, dict) and 'periods' in candle_data:
                logger.debug(f"📊 Processing candle {candle_time} with {len(candle_data['periods'])} periods")
                for period_time, period_data in candle_data['periods'].items():
                    if 'indicators' in period_data and period_data['indicators']:
                        logger.debug(f"📊 Found {len(period_data['indicators'])} indicators for {period_time}")
                        for indicator, value in period_data['indicators'].items():
                            # Extract category from indicator name
                            parts = indicator.split('_')
                            category = parts[0] if parts else 'unknown'
                            all_indicators[indicator] = category
                    else:
                        logger.debug(f"⚠️ No indicators found for {period_time}")

                    # Collect price data
                    if 'price_data' in period_data:
                        price_data_collected[period_time] = period_data['price_data']
                        logger.debug(f"📊 Collected price data for {period_time}")
            else:
                logger.debug(f"⚠️ Candle {candle_time} has no periods data")

        if not all_indicators:
            logger.warning("⚠️ No indicators found for candles analysis")
            # Still create a sheet with just price data if we have that
            if price_data_collected:
                logger.info("📊 Creating sheet with price data only")
            else:
                logger.warning("⚠️ No price data either, skipping candles sheet")
                return False

        # Create the data structure: indicators as rows, time periods as columns
        sheet_data = []

        # First, add price data rows
        price_metrics = ['Open', 'High', 'Low', 'Close', 'Volume']
        for metric in price_metrics:
            row = {'Indicator': metric, 'Category': 'Price'}
            for time_period in all_time_periods:
                if time_period in price_data_collected:
                    # Handle both 'Volume' and 'volume' keys in price data
                    if metric == 'Volume':
                        value = price_data_collected[time_period].get('Volume',
                                price_data_collected[time_period].get('volume', ''))
                    else:
                        value = price_data_collected[time_period].get(metric, '')
                    row[time_period] = value
                else:
                    row[time_period] = ''
            sheet_data.append(row)

        # Add separator row
        separator_row = {'Indicator': '--- TECHNICAL INDICATORS ---', 'Category': '---'}
        for time_period in all_time_periods:
            separator_row[time_period] = '---'
        sheet_data.append(separator_row)

        # Then add indicator rows
        for indicator, category in sorted(all_indicators.items()):
            row = {'Indicator': indicator, 'Category': category}

            # Collect values for this indicator across all time periods
            for time_period in all_time_periods:
                value = ''
                # Find this indicator's value for this time period
                for _, candle_data in results['candles'].items():
                    if isinstance(candle_data, dict) and 'periods' in candle_data:
                        if time_period in candle_data['periods']:
                            period_data = candle_data['periods'][time_period]
                            if 'indicators' in period_data and indicator in period_data['indicators']:
                                value = period_data['indicators'][indicator]
                                break
                row[time_period] = value

            sheet_data.append(row)

        # Create DataFrame and export
        if sheet_data:
            candles_df = pd.DataFrame(sheet_data)

            # Reorder columns: Indicator, Category, then time periods in chronological order
            column_order = ['Indicator', 'Category'] + all_time_periods
            candles_df = candles_df[column_order]

            candles_df.to_excel(writer, sheet_name='Candles_Analysis', index=False)
            logger.info(f"✅ Candles sheet created with {len(sheet_data)} rows and {len(all_time_periods)} time periods")
            return True

        return False

    def run_historical_backtest_with_indicators(self, ticker: str, exchange: str, date: str,
                                              start_time: str = "09:15", end_time: str = "15:30",
                                              method: str = 'extension', categories: List[str] = None,
                                              export_excel: bool = True) -> Dict:
        """
        Run historical backtest with technical indicators analysis
        """
        try:
            logger.info(f"🔄 Running historical backtest with indicators for {ticker} on {exchange}")

            # Import backtester
            sys.path.append(current_dir)
            smart_backtester = __import__('smart_vectorized_backtester copy')
            SmartVectorizedBacktester = smart_backtester.SmartVectorizedBacktester

            # Get token info
            token_info = self.get_token_info(ticker, exchange)
            if not token_info:
                return {'error': 'Could not get token info'}

            # Run backtester
            backtester = SmartVectorizedBacktester(
                ticker=ticker,
                exchange=exchange,
                start=start_time,
                end=end_time,
                date=date,
                tokenid=token_info['token']
            )

            # Get market data
            market_data = self.get_market_data(ticker, exchange, date, start_time, end_time)
            if market_data is None:
                return {'error': 'Failed to get market data'}

            # Run full session analysis
            full_analysis = self.indicators_analyzer._analyze_dataframe(market_data, method, categories)

            # TODO: Integrate with actual backtester signals
            # For now, simulate some signal times
            signal_times = ["10:30", "12:15", "14:45"]

            # Analyze indicators at signal times
            signal_analyses = {}
            for signal_time in signal_times:
                # Simplified: analyze middle portion of data
                mid_index = len(market_data) // 2
                signal_data = market_data.iloc[max(0, mid_index-2):mid_index+1]
                signal_analysis = self.indicators_analyzer._analyze_dataframe(signal_data, method, categories)
                signal_analyses[signal_time] = signal_analysis

            # Combine results
            results = {
                'method': 'historical_backtest',
                'full_session_analysis': full_analysis,
                'signal_analyses': signal_analyses,
                'total_signals': len(signal_times),
                'metadata': {
                    'ticker': ticker,
                    'exchange': exchange,
                    'date': date,
                    'mode': 'historical_backtest',
                    'method': method,
                    'categories': categories,
                    'data_points': len(market_data),
                    'time_range': f"{start_time} - {end_time}",
                    'analysis_timestamp': datetime.now().isoformat(),
                    'token_info': token_info
                }
            }

            # Export to Excel if requested
            if export_excel:
                try:
                    excel_file = self.export_to_excel(results)
                    if excel_file:
                        results['excel_file'] = excel_file
                        logger.info(f"✅ Excel export successful: {excel_file}")
                    else:
                        logger.warning("⚠️ Excel export failed, but analysis completed successfully")
                except Exception as e:
                    logger.warning(f"⚠️ Excel export error (analysis still successful): {str(e)}")
                    results['excel_export_error'] = str(e)

            return results

        except Exception as e:
            logger.error(f"❌ Error in historical backtest: {str(e)}")
            return {'error': str(e)}

    def run_live_market_monitor_with_indicators(self, tickers_info: List[Dict],
                                               method: str = 'extension', categories: List[str] = None,
                                               check_interval: int = 60, export_excel: bool = True) -> Dict:
        """
        Run live market monitoring with technical indicators analysis
        """
        try:
            logger.info(f"🔴 Starting live market monitoring with indicators")
            logger.info(f"📊 Monitoring {len(tickers_info)} tickers")

            # Import live monitoring function
            sys.path.append(current_dir)
            smart_backtester = __import__('smart_vectorized_backtester copy')
            run_live_market_monitor = smart_backtester.run_live_market_monitor

            # TODO: Integrate with actual live monitoring
            # For now, simulate live analysis
            live_results = {}

            for ticker_info in tickers_info:
                ticker = ticker_info['ticker']
                exchange = ticker_info['exchange']

                logger.info(f"📈 Analyzing {ticker} on {exchange}")

                # Get current market data (last few minutes)
                current_date = datetime.now().strftime("%d-%m-%Y")
                current_time = datetime.now().strftime("%H:%M")

                # Simulate getting recent data
                market_data = self.get_market_data(ticker, exchange, current_date, "09:15", current_time)
                if market_data is not None and len(market_data) > 0:
                    # Analyze recent data
                    recent_analysis = self.indicators_analyzer._analyze_dataframe(
                        market_data.tail(10), method, categories  # Last 10 candles
                    )

                    live_results[f"{ticker}_{exchange}"] = {
                        'analysis': recent_analysis,
                        'timestamp': datetime.now().isoformat(),
                        'data_points': len(market_data)
                    }

            # Combine results
            results = {
                'method': 'live_monitoring',
                'live_analyses': live_results,
                'total_tickers': len(tickers_info),
                'metadata': {
                    'mode': 'live_monitoring',
                    'method': method,
                    'categories': categories,
                    'check_interval': check_interval,
                    'analysis_timestamp': datetime.now().isoformat(),
                    'tickers': tickers_info
                }
            }

            # Export to Excel if requested
            if export_excel:
                excel_file = self.export_to_excel(results)
                results['excel_file'] = excel_file

            return results

        except Exception as e:
            logger.error(f"❌ Error in live monitoring: {str(e)}")
            return {'error': str(e)}

def create_cli_parser():
    """Create CLI argument parser"""
    parser = argparse.ArgumentParser(
        description='Integrated Technical Indicators Analyzer with Smart Backtester',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Universal Time-Series Format (Indicator|Category|Time1|Time2|...) - Works with ALL methods
  python integrated_technical_analyzer.py --mode analysis --analysis-type full --ticker BATAINDIA --exchange BSE --date 24-06-2025 --method extension

  # Automated All Indicators (280+ indicators) with Universal Time-Series Format
  python integrated_technical_analyzer.py --mode analysis --analysis-type full --ticker BATAINDIA --exchange BSE --date 24-06-2025 --method strategy_all_automated

  # Candles analysis with specific times (auto-includes 2min history)
  python integrated_technical_analyzer.py --mode analysis --analysis-type candles --ticker BATAINDIA --exchange BSE --date 24-06-2025 --times "12:30,14:15"

  # MCX analysis (auto-detects 09:30-23:50 hours)
  python integrated_technical_analyzer.py --mode analysis --analysis-type full --ticker SILVER --exchange MCX --date 24-06-2025

  # Custom time range override
  python integrated_technical_analyzer.py --mode analysis --analysis-type period --ticker RELIANCE --exchange NSE --date 24-06-2025 --start-time 10:00 --end-time 14:00

  # Historical backtest with time-series indicators
  python integrated_technical_analyzer.py --mode historical --ticker BATAINDIA --exchange BSE --date 24-06-2025

  # Live market monitoring
  python integrated_technical_analyzer.py --mode live --tickers "BATAINDIA,BSE,RELIANCE,NSE"

Note: All analysis types now generate universal time-series Excel format: Indicator | Category | 09:15 | 09:16 | 09:17 | ...
        """
    )

    # Main mode selection
    parser.add_argument('--mode', choices=['historical', 'live', 'analysis'],
                       help='Operation mode')

    # Common parameters
    parser.add_argument('--ticker', help='Stock ticker symbol')
    parser.add_argument('--exchange', choices=['NSE', 'BSE', 'MCX', 'NFO', 'CUSTOM'],
                       help='Exchange name')
    parser.add_argument('--date', help='Date in DD-MM-YYYY format')

    # Time parameters (auto-detected based on exchange)
    parser.add_argument('--start-time', help='Start time (auto: NSE/BSE/NFO=09:15, MCX=09:30)')
    parser.add_argument('--end-time', help='End time (auto: NSE/BSE/NFO=15:30, MCX=23:50)')
    parser.add_argument('--interval', choices=['1', '3', '5', '10', '15', '30', '60', '120', '240'],
                       default='1', help='Candle interval in minutes (default: 1)')

    # Analysis parameters
    parser.add_argument('--analysis-type', choices=['signals', 'candles', 'period', 'full'],
                       default='full', help='Analysis type (default: full)')
    parser.add_argument('--method', choices=['direct_call', 'extension', 'extension_kind',
                                           'strategy_all', 'strategy_all_automated', 'strategy_common', 'strategy_category', 'custom_strategy'],
                       default='extension', help='Analysis method (default: extension)')

    # Category selection
    parser.add_argument('--categories', help='Comma-separated list of categories')
    parser.add_argument('--exclude-categories', help='Comma-separated list of categories to exclude')
    parser.add_argument('--list-categories', action='store_true', help='List all available categories')

    # Function selection (alternative to categories)
    parser.add_argument('--functions', help='Comma-separated list of specific function names (e.g., "rsi,macd,bbands"). Overrides categories if provided.')

    # Specific analysis options
    parser.add_argument('--times', help='Comma-separated candle times for candles mode (e.g., "12:23,15:42")')
    parser.add_argument('--include-history', action='store_true', default=True,
                       help='Include historical context (default: True)')

    # Live monitoring options
    parser.add_argument('--tickers', help='Comma-separated ticker,exchange pairs (e.g., "BATAINDIA,BSE,RELIANCE,NSE")')
    parser.add_argument('--check-interval', type=int, default=60, help='Check interval in seconds (default: 60)')

    # Export options
    parser.add_argument('--export-excel', action='store_true', default=True,
                       help='Export results to Excel (default: True)')
    parser.add_argument('--output-file', help='Custom output filename')
    parser.add_argument('--no-export', action='store_true', help='Disable Excel export')

    # Note: Always uses fresh login (no caching)

    # Logging
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')

    return parser

def main():
    """Main function for CLI execution"""
    parser = create_cli_parser()
    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize analyzer
    analyzer = IntegratedTechnicalAnalyzer()

    # Handle list categories option
    if args.list_categories:
        print("📂 Available Categories:")
        for i, category in enumerate(analyzer.indicators_analyzer.categories, 1):
            indicators_count = len(analyzer.indicators_analyzer.category_indicators.get(category, []))
            print(f"  {i}. {category} ({indicators_count} indicators)")
        print(f"\n💡 Usage: --categories overlap,momentum,volatility")
        print(f"💡 Usage: --exclude-categories candles,cycles")
        return

    # Validate required arguments for operation modes
    if not args.mode:
        print("❌ Error: --mode is required for analysis operations")
        print("💡 Use --list-categories to see available categories")
        print("💡 Available modes: historical, live, analysis")
        return

    # Process category selection
    categories = None
    if args.categories:
        categories = [cat.strip() for cat in args.categories.split(',')]
        # Validate categories
        invalid_cats = [cat for cat in categories if cat not in analyzer.indicators_analyzer.categories]
        if invalid_cats:
            print(f"❌ Invalid categories: {', '.join(invalid_cats)}")
            print(f"📂 Available categories: {', '.join(analyzer.indicators_analyzer.categories)}")
            return
    elif args.exclude_categories:
        exclude_cats = [cat.strip() for cat in args.exclude_categories.split(',')]
        categories = [cat for cat in analyzer.indicators_analyzer.categories if cat not in exclude_cats]

    # Process function selection (overrides categories if provided)
    functions = None
    if args.functions:
        functions = [func.strip() for func in args.functions.split(',')]
        logger.info(f"🔧 Using specific functions: {', '.join(functions)}")
        # Note: We don't validate function names here as pandas-ta has many functions
        # and validation will happen during execution

    # Handle export settings
    export_excel = args.export_excel and not args.no_export

    # Execute based on mode
    results = None

    try:
        if args.mode == 'historical':
            # Historical backtest mode
            if not all([args.ticker, args.exchange, args.date]):
                print("❌ Error: --ticker, --exchange, and --date are required for historical mode")
                return

            results = analyzer.run_historical_backtest_with_indicators(
                ticker=args.ticker,
                exchange=args.exchange,
                date=args.date,
                start_time=args.start_time,
                end_time=args.end_time,
                method=args.method,
                categories=categories,
                export_excel=export_excel
            )

        elif args.mode == 'live':
            # Live monitoring mode
            if not args.tickers:
                print("❌ Error: --tickers is required for live mode")
                print("💡 Example: --tickers 'BATAINDIA,BSE,RELIANCE,NSE'")
                return

            # Parse tickers
            ticker_pairs = args.tickers.split(',')
            if len(ticker_pairs) % 2 != 0:
                print("❌ Error: Tickers must be in ticker,exchange pairs")
                return

            tickers_info = []
            for i in range(0, len(ticker_pairs), 2):
                tickers_info.append({
                    'ticker': ticker_pairs[i].strip(),
                    'exchange': ticker_pairs[i+1].strip()
                })

            results = analyzer.run_live_market_monitor_with_indicators(
                tickers_info=tickers_info,
                method=args.method,
                categories=categories,
                check_interval=args.check_interval,
                export_excel=export_excel
            )

        elif args.mode == 'analysis':
            # Analysis mode
            if not all([args.ticker, args.exchange, args.date]):
                print("❌ Error: --ticker, --exchange, and --date are required for analysis mode")
                return

            # Handle specific analysis types
            candle_times = None
            if args.analysis_type == 'candles' and args.times:
                candle_times = [time.strip() for time in args.times.split(',')]

            results = analyzer.analyze_with_market_data(
                ticker=args.ticker,
                exchange=args.exchange,
                date=args.date,
                mode=args.analysis_type,
                method=args.method,
                categories=categories,
                functions=functions,
                start_time=args.start_time,
                end_time=args.end_time,
                candle_times=candle_times,
                include_history=args.include_history,
                interval=args.interval
            )

            # Export to Excel if requested
            if export_excel and results and 'error' not in results:
                excel_file = analyzer.export_to_excel(results, args.output_file)
                if excel_file:
                    results['excel_file'] = excel_file

        # Display results
        if results:
            if 'error' in results:
                print(f"❌ Error: {results['error']}")
            else:
                print(f"\n✅ Analysis completed successfully!")

                # Display summary
                metadata = results.get('metadata', {})
                print(f"📊 Ticker: {metadata.get('ticker', 'Unknown')}")
                print(f"🏢 Exchange: {metadata.get('exchange', 'Unknown')}")
                print(f"📅 Date: {metadata.get('date', 'Unknown')}")
                print(f"🔧 Method: {metadata.get('method', 'Unknown')}")
                categories_list = metadata.get('categories', [])
                if isinstance(categories_list, list):
                    print(f"📂 Categories: {', '.join(categories_list)}")
                else:
                    print(f"📂 Categories: {categories_list}")

                # Display indicator counts
                if 'indicators' in results:
                    print(f"📈 Total Indicators: {len(results['indicators'])}")
                if 'total_signals' in results:
                    print(f"🎯 Total Signals: {results['total_signals']}")
                if 'total_candles' in results:
                    print(f"🕯️ Total Candles: {results['total_candles']}")

                # Display Excel file info
                if 'excel_file' in results:
                    print(f"📄 Excel Export: {results['excel_file']}")

                print(f"⏰ Analysis Time: {metadata.get('analysis_timestamp', 'Unknown')}")

    except KeyboardInterrupt:
        print(f"\n⚠️ Analysis interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        logger.error(f"Unexpected error: {str(e)}")

if __name__ == "__main__":
    main()
