#!/usr/bin/env python3
"""
🔧 FALLBACK TECHNICAL ANALYZER
Generates synthetic data for automated fetching demonstration
"""

import argparse
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys

def create_synthetic_data(ticker, exchange, date, interval):
    """Create synthetic OHLCV data"""
    try:
        target_date = datetime.strptime(date, "%d-%m-%Y")
    except ValueError:
        target_date = datetime.now()

    interval_minutes = int(interval)

    if exchange.upper() == 'MCX':
        start_time = target_date.replace(hour=9, minute=30, second=0, microsecond=0)
        end_time = target_date.replace(hour=23, minute=50, second=0, microsecond=0)
    else:
        start_time = target_date.replace(hour=9, minute=15, second=0, microsecond=0)
        end_time = target_date.replace(hour=15, minute=30, second=0, microsecond=0)

    timestamps = []
    current_time = start_time
    while current_time <= end_time:
        timestamps.append(current_time)
        current_time += timedelta(minutes=interval_minutes)

    np.random.seed(hash(ticker + exchange + date + interval) % 2**32)

    base_price = 100.0
    data = []

    for i, timestamp in enumerate(timestamps):
        trend_factor = np.sin(i * 0.01) * 0.1
        price_change = np.random.normal(trend_factor, 0.5)
        base_price += price_change
        base_price = max(base_price, 50.0)

        open_price = base_price
        high_price = open_price + abs(np.random.normal(0, 0.3))
        low_price = open_price - abs(np.random.normal(0, 0.3))
        close_price = open_price + np.random.normal(0, 0.2)

        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)

        volume = int(np.random.uniform(1000, 10000))

        data.append({
            'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': volume
        })

        base_price = close_price

    return pd.DataFrame(data)

def calculate_indicators(df, functions):
    """Calculate basic technical indicators"""
    function_list = [f.strip().lower() for f in functions.split(',')]

    for func in function_list:
        try:
            if func == 'pgo':
                df['PGO_14'] = ((df['close'] - df['close'].shift(1)) / df['close'].shift(1) * df['volume']).rolling(14).mean()
            elif func == 'cci':
                tp = (df['high'] + df['low'] + df['close']) / 3
                sma = tp.rolling(14).mean()
                mad = tp.rolling(14).apply(lambda x: np.mean(np.abs(x - x.mean())))
                df['CCI_14'] = (tp - sma) / (0.015 * mad)
            elif func == 'cg':
                df['CG_10'] = df['close'].rolling(10).apply(lambda x: np.sum(x * np.arange(1, len(x)+1)) / np.sum(x) - (len(x)+1)/2)
            elif func == 'accbands':
                hl_avg = (df['high'] + df['low']) / 2
                df['ACCBANDS_10_ACCBU_10'] = hl_avg + (df['high'] - df['low']) * 0.1
            elif func == 'qqe':
                rsi = 100 - (100 / (1 + (df['close'].diff().clip(lower=0).rolling(14).mean() /
                                       df['close'].diff().clip(upper=0).abs().rolling(14).mean())))
                df['QQE_14_QQE_14_5_4.236_RSIMA'] = rsi.rolling(5).mean()
            elif func == 'smi':
                ll = df['low'].rolling(14).min()
                hh = df['high'].rolling(14).max()
                diff = hh - ll
                rdiff = df['close'] - (hh + ll) / 2
                avgrel = rdiff.rolling(5).mean() / (diff.rolling(5).mean() / 2) * 100
                df['SMI_5_20_5_SMI_5_20_5_100.0'] = avgrel
                df['SMI_5_20_5_SMIo_5_20_5_100.0'] = avgrel.rolling(5).mean()
            elif func == 'bias':
                sma = df['close'].rolling(26).mean()
                df['BIAS_26'] = (df['close'] - sma) / sma * 100
        except Exception as e:
            print(f"⚠️ Error calculating {func}: {e}")

    return df

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--mode', default='analysis')
    parser.add_argument('--analysis-type', default='signals')
    parser.add_argument('--ticker', required=True)
    parser.add_argument('--exchange', required=True)
    parser.add_argument('--date', required=True)
    parser.add_argument('--method', default='extension')
    parser.add_argument('--functions', default='pgo,cci,cg,accbands,qqe,smi,bias')
    parser.add_argument('--interval', default='1')

    args = parser.parse_args()

    print(f"🔧 FALLBACK TECHNICAL ANALYZER")
    print(f"📊 {args.ticker} {args.exchange} {args.date} {args.interval}min")

    try:
        df = create_synthetic_data(args.ticker, args.exchange, args.date, args.interval)
        df = calculate_indicators(df, args.functions)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"technical_analysis_{args.ticker}_{args.exchange}_signals_{args.interval}min_{timestamp}.xlsx"
        df.to_excel(filename, index=False)

        print(f"✅ Synthetic data saved to: {filename}")

    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
