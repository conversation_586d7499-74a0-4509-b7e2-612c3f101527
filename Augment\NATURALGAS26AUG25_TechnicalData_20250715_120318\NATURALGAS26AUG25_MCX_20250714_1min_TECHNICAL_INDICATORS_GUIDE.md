# Technical Indicators Analyzer - Comprehensive Guide

## Overview

The Technical Indicators Analyzer is a comprehensive system that uses pandas-ta library to analyze technical indicators for different scenarios. It supports multiple analysis types and implementation methods to provide detailed insights into market data.

## Features

### 🎯 Analysis Types
1. **Signals Analysis** - Analyze indicators at candles where signals are generated by the backtester
2. **Specific Candles** - Analyze indicators at user-specified candle times
3. **Time Period** - Analyze indicators for a specific time range
4. **Full Market Session** - Analyze indicators for complete market hours

### 🔧 Implementation Methods
1. **Standard** - TA-Lib style convention (manual indicator calculation)
2. **Extension** - DataFrame ta extension convention (df.ta.indicator())
3. **Study** - Pandas TA Study convention (recommended for comprehensive analysis)
4. **Strategy All** - All available indicators using AllStrategy
5. **Strategy Common** - Common indicators using CommonStrategy
6. **Custom Strategy** - Custom strategy with selected indicators

### 📊 Supported Indicators

The system supports 150+ technical indicators across multiple categories:

- **Overlap/Moving Averages**: SMA, EMA, WMA, HMA, VWMA, etc.
- **Momentum**: RSI, MACD, Stochastic, CCI, Williams %R, etc.
- **Volatility**: Bollinger Bands, ATR, Keltner Channels, etc.
- **Volume**: OBV, AD, CMF, MFI, VWAP, etc.
- **Trend**: ADX, Aroon, PSAR, SuperTrend, etc.
- **Statistics**: Z-Score, Standard Deviation, Skewness, etc.
- **Performance**: Returns, Drawdown, etc.

## Installation

### Prerequisites
```bash
# Activate Shoonya1 conda environment
conda activate Shoonya1

# Ensure pandas-ta is installed
pip install pandas-ta
```

### Files Required
- `technical_indicators_analyzer.py` - Main analyzer class
- `smart_vectorized_backtester copy.py` - For signal generation
- `test_technical_indicators.py` - Test suite

## Usage

### Command Line Interface

#### 1. Analyze Signals from Backtester
```bash
python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025
```

#### 2. Analyze Specific Candles
```bash
python technical_indicators_analyzer.py --mode candles --ticker BATAINDIA --exchange BSE --date 24-06-2025 --times "12:23,15:42"
```

#### 3. Analyze Time Period
```bash
python technical_indicators_analyzer.py --mode period --ticker BATAINDIA --exchange BSE --date 24-06-2025 --start-time "10:15" --end-time "15:00"
```

#### 4. Analyze Full Market Session
```bash
python technical_indicators_analyzer.py --mode full --ticker BATAINDIA --exchange BSE --date 24-06-2025
```

### Method Selection
```bash
# Use different analysis methods
python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --method study
python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --method strategy_all
```

### Additional Options
```bash
# Disable historical data inclusion
python technical_indicators_analyzer.py --mode candles --ticker BATAINDIA --exchange BSE --date 24-06-2025 --times "12:23" --no-history

# Save results to specific file
python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --save "my_analysis.json"

# Enable verbose logging
python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --verbose
```

## Programmatic Usage

### Basic Example
```python
from technical_indicators_analyzer import TechnicalIndicatorsAnalyzer

# Initialize analyzer
analyzer = TechnicalIndicatorsAnalyzer()

# Analyze signals from backtester
results = analyzer.analyze_signals_from_backtester(
    ticker="BATAINDIA",
    exchange="BSE", 
    date="24-06-2025",
    method="study"
)

# Print summary
analyzer.print_summary(results)

# Save results
analyzer.save_results(results, "analysis_results.json")
```

### Advanced Example
```python
# Analyze specific candles with historical context
results = analyzer.analyze_specific_candles(
    ticker="BATAINDIA",
    exchange="BSE",
    date="24-06-2025", 
    candle_times=["12:23", "15:42"],
    method="study",
    include_history=True
)

# Access detailed results
for candle_analysis in results['analysis']:
    candle_time = candle_analysis['candle_time']
    indicators = candle_analysis['indicators']
    print(f"Candle {candle_time}: {len(indicators)} indicators")
```

## Output Format

### JSON Structure
```json
{
  "ticker": "BATAINDIA",
  "date": "24-06-2025",
  "method": "study",
  "analysis": [
    {
      "signal_time": "12:23",
      "signal_type": "CALL",
      "target_candle": {
        "time": "12:23",
        "open": 1000.0,
        "high": 1005.0,
        "low": 998.0,
        "close": 1003.0,
        "volume": 5000
      },
      "indicators": {
        "SMA_20": 1001.5,
        "EMA_20": 1002.1,
        "RSI_14": 65.4,
        "MACD_12_26_9": 2.3,
        "BBL_20_2.0": 995.2,
        "BBM_20_2.0": 1001.0,
        "BBU_20_2.0": 1006.8,
        "ATR_14": 8.5,
        "ADX_14": 25.3
      },
      "data_points_used": 45,
      "include_history": true
    }
  ],
  "total_signals": 1
}
```

## Key Benefits

### 1. Historical Context Analysis
- Includes 2 minutes of historical data before each signal/candle
- Enables trend analysis and pattern recognition
- Helps understand indicator changes leading to signals

### 2. Multiple Implementation Methods
- Compare results across different calculation approaches
- Validate indicator accuracy
- Choose optimal method for specific use cases

### 3. Comprehensive Indicator Coverage
- 150+ technical indicators
- All major categories covered
- Custom strategy creation capability

### 4. Flexible Analysis Modes
- Signal-driven analysis for backtester integration
- Time-based analysis for specific periods
- Custom candle selection for targeted analysis

## Testing

Run the test suite to verify functionality:
```bash
python test_technical_indicators.py
```

This will test:
- Basic functionality with sample data
- Individual indicator calculations
- Categories and indicators listing
- Custom study creation

## Performance Considerations

### Method Performance (fastest to slowest)
1. **Standard** - Manual calculation, fastest
2. **Extension** - DataFrame extension, moderate
3. **Custom Strategy** - Selected indicators, moderate
4. **Study** - Comprehensive analysis, slower
5. **Strategy Common** - Common indicators, slower
6. **Strategy All** - All indicators, slowest

### Optimization Tips
- Use `cores=0` in studies to disable multiprocessing for stability
- For large datasets, consider using Standard or Extension methods
- Cache results for repeated analysis of same data

## Troubleshooting

### Common Issues
1. **Import Error**: Ensure pandas-ta is installed and Shoonya1 environment is active
2. **No Data**: Verify ticker symbol, exchange, and date format
3. **Indicator Calculation Error**: Check data quality and minimum data requirements
4. **Memory Issues**: Use smaller time periods or fewer indicators

### Debug Mode
Enable verbose logging for detailed troubleshooting:
```bash
python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --verbose
```

## Future Enhancements

### Planned Features
1. **Real-time Analysis** - Live market data integration
2. **Pattern Recognition** - Automated pattern detection
3. **Signal Prediction** - ML-based signal forecasting
4. **Custom Indicators** - User-defined indicator creation
5. **Visualization** - Interactive charts and plots

### AI/ML Integration
The system is designed to support future AI/ML features:
- Feature extraction from indicator changes
- Candle prediction models
- Signal strength analysis
- Market regime detection
