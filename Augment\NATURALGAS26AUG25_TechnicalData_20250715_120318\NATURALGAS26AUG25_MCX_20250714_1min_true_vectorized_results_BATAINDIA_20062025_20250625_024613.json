{"success": true, "ticker": "BATAINDIA", "analysis_period": "12:00-12:30", "date": "20-06-2025", "execution_time_seconds": 2.822013, "api_calls_used": 1, "performance_improvement_factor": 197.73119400938265, "api_reduction_factor": 62.0, "efficiency_gain_percent": 19673.119400938263, "total_signals_generated": 15, "minutes_analyzed": 31, "minute_signals": [{"time": "12:00", "datetime": "2025-06-20 12:00:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:18", "window_end": "12:00", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:48", "window_end": "12:00", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:01", "datetime": "2025-06-20 12:01:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:19", "window_end": "12:01", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "10:49", "window_end": "12:01", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:02", "datetime": "2025-06-20 12:02:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:20", "window_end": "12:02", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "10:50", "window_end": "12:02", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:03", "datetime": "2025-06-20 12:03:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:21", "window_end": "12:03", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "10:51", "window_end": "12:03", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:04", "datetime": "2025-06-20 12:04:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:22", "window_end": "12:04", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "10:52", "window_end": "12:04", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:05", "datetime": "2025-06-20 12:05:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:23", "window_end": "12:05", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "10:53", "window_end": "12:05", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:06", "datetime": "2025-06-20 12:06:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:24", "window_end": "12:06", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "10:54", "window_end": "12:06", "data_points": 73}, "final_signal": 0, "signal_reason": "Unknown reason", "signal_type": "NONE"}, {"time": "12:07", "datetime": "2025-06-20 12:07:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:25", "window_end": "12:07", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "10:55", "window_end": "12:07", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "12:08", "datetime": "2025-06-20 12:08:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:26", "window_end": "12:08", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "10:56", "window_end": "12:08", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "12:09", "datetime": "2025-06-20 12:09:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:27", "window_end": "12:09", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "10:57", "window_end": "12:09", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "12:10", "datetime": "2025-06-20 12:10:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:28", "window_end": "12:10", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "10:58", "window_end": "12:10", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "12:11", "datetime": "2025-06-20 12:11:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:29", "window_end": "12:11", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "10:59", "window_end": "12:11", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:12", "datetime": "2025-06-20 12:12:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:30", "window_end": "12:12", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:00", "window_end": "12:12", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:13", "datetime": "2025-06-20 12:13:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:31", "window_end": "12:13", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:01", "window_end": "12:13", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:14", "datetime": "2025-06-20 12:14:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:32", "window_end": "12:14", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:02", "window_end": "12:14", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:15", "datetime": "2025-06-20 12:15:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:33", "window_end": "12:15", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:03", "window_end": "12:15", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:16", "datetime": "2025-06-20 12:16:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:34", "window_end": "12:16", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:04", "window_end": "12:16", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:17", "datetime": "2025-06-20 12:17:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:35", "window_end": "12:17", "data_points": 43}, "stage2": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:05", "window_end": "12:17", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:18", "datetime": "2025-06-20 12:18:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:36", "window_end": "12:18", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:06", "window_end": "12:18", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:19", "datetime": "2025-06-20 12:19:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:37", "window_end": "12:19", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:07", "window_end": "12:19", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:20", "datetime": "2025-06-20 12:20:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:38", "window_end": "12:20", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:08", "window_end": "12:20", "data_points": 73}, "final_signal": 1, "signal_reason": "Both stages agree: CALL", "signal_type": "CALL"}, {"time": "12:21", "datetime": "2025-06-20 12:21:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:39", "window_end": "12:21", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:09", "window_end": "12:21", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:22", "datetime": "2025-06-20 12:22:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:40", "window_end": "12:22", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:10", "window_end": "12:22", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:23", "datetime": "2025-06-20 12:23:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:41", "window_end": "12:23", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:11", "window_end": "12:23", "data_points": 73}, "final_signal": 0, "signal_reason": "Signal conflict: Stage1=-1, Stage2=0", "signal_type": "NONE"}, {"time": "12:24", "datetime": "2025-06-20 12:24:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:42", "window_end": "12:24", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:12", "window_end": "12:24", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:25", "datetime": "2025-06-20 12:25:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:43", "window_end": "12:25", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:13", "window_end": "12:25", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:26", "datetime": "2025-06-20 12:26:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:44", "window_end": "12:26", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:14", "window_end": "12:26", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:27", "datetime": "2025-06-20 12:27:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:45", "window_end": "12:27", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:15", "window_end": "12:27", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:28", "datetime": "2025-06-20 12:28:00", "stage1": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:46", "window_end": "12:28", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:16", "window_end": "12:28", "data_points": 73}, "final_signal": -1, "signal_reason": "Both stages agree: PUT", "signal_type": "PUT"}, {"time": "12:29", "datetime": "2025-06-20 12:29:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:47", "window_end": "12:29", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:17", "window_end": "12:29", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0", "signal_type": "NONE"}, {"time": "12:30", "datetime": "2025-06-20 12:30:00", "stage1": {"pass": false, "sideways": "True", "nadarya_signal": 0, "reason": "Sideways: True, Nadarya: False", "window_start": "11:48", "window_end": "12:30", "data_points": 43}, "stage2": {"pass": true, "sideways": "True", "nadarya_signal": -1, "reason": "Sideways: <PERSON>, <PERSON><PERSON><PERSON>: True", "window_start": "11:18", "window_end": "12:30", "data_points": 73}, "final_signal": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0", "signal_type": "NONE"}], "ver4_logic_preserved": true, "true_vectorization_achieved": true}