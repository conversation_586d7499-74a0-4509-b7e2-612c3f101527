{"ticker": "BATAINDIA", "exchange": "NSE", "date": "23-06-2025", "start_time": "10:12", "end_time": "10:30", "tokenid": "371", "minute_signals": [{"minute": 1, "time": "10:12", "datetime": "2025-06-23 10:12:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 2, "time": "10:13", "datetime": "2025-06-23 10:13:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 3, "time": "10:14", "datetime": "2025-06-23 10:14:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 4, "time": "10:15", "datetime": "2025-06-23 10:15:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 5, "time": "10:16", "datetime": "2025-06-23 10:16:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 6, "time": "10:17", "datetime": "2025-06-23 10:17:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 7, "time": "10:18", "datetime": "2025-06-23 10:18:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 8, "time": "10:19", "datetime": "2025-06-23 10:19:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 9, "time": "10:20", "datetime": "2025-06-23 10:20:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 10, "time": "10:21", "datetime": "2025-06-23 10:21:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 11, "time": "10:22", "datetime": "2025-06-23 10:22:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 12, "time": "10:23", "datetime": "2025-06-23 10:23:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 13, "time": "10:24", "datetime": "2025-06-23 10:24:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 14, "time": "10:25", "datetime": "2025-06-23 10:25:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 15, "time": "10:26", "datetime": "2025-06-23 10:26:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 16, "time": "10:27", "datetime": "2025-06-23 10:27:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 17, "time": "10:28", "datetime": "2025-06-23 10:28:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 18, "time": "10:29", "datetime": "2025-06-23 10:29:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}, {"minute": 19, "time": "10:30", "datetime": "2025-06-23 10:30:00", "has_position": false, "position_type": null, "stage1_checked": true, "stage1_sideways": "True", "stage1_nadarya": 0, "stage1_pass": false, "stage2_checked": true, "stage2_sideways": "True", "stage2_nadarya": 0, "stage2_pass": false, "signal_generated": 0, "signal_reason": "Stage1 fail: sideways=True, nadarya=0; Stage2 fail: sideways=True, nadarya=0", "action_taken": "NO_SIGNAL", "notes": ""}], "position_events": [], "summary": {"total_minutes": 19, "signals_generated": 0, "positions_opened": 0, "positions_closed": 0}}